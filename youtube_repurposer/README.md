# Enhanced YouTube Repurposer - Universal Content Application Template

A powerful Flask-based web application that transforms YouTube videos and transcripts into engaging short-form content using advanced AI workflows. This project serves as a comprehensive template for building content processing applications with integrated PocketFlow workflows, Enhanced RAG capabilities, and Context Engineering.

## 🚀 Key Features

### Core Functionality
- **Video Processing**: Upload and process video files with FFmpeg integration
- **DeepSeek R1 Integration**: Advanced content analysis with 128k token context window for long-form content
- **WhisperX Transcription**: High-accuracy local audio transcription with speaker diarization
- **Smart Chunking**: Intelligent text segmentation for long YouTube videos (1+ hours)
- **Transcript Analysis**: Advanced AI-powered content analysis using multiple LLM providers
- **Short Video Generation**: Intelligent content segmentation and repurposing
- **Metadata Extraction**: Automated titles, descriptions, and key segment identification
- **Progress Tracking**: Real-time progress updates for long-running tasks

### Enhanced Template System
- **PocketFlow Integration**: Modular workflow system for complex AI processing
- **Enhanced RAG**: Vector embeddings with semantic search and web crawling
- **Context Engineering**: Advanced prompting strategies and context optimization
- **Content Analysis Agents**: Specialized AI agents for different content types
- **Universal Template**: Easily create new content applications from this base

### Advanced Capabilities
- **Multi-Platform Optimization**: Content tailored for YouTube, TikTok, Instagram, etc.
- **Engagement Analysis**: AI-powered engagement prediction and optimization
- **Thumbnail Generation**: Intelligent thumbnail concept creation
- **SEO Optimization**: Automated SEO-friendly content generation
- **Batch Processing**: Handle multiple videos and content pieces efficiently

## 🛠 Quick Setup

### Automated Setup (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd youtube_repurposer
```

2. Install WhisperX for transcription:
```bash
python install_whisperx.py
```

3. Run the enhanced setup script:
```bash
python setup_enhanced.py
```

4. Update your API keys in `.env`:
```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
```

5. Start the application:
```bash
python app.py
```

### Manual Setup

1. **Prerequisites**:
   - Python 3.8+
   - FFmpeg (for video processing)
   - API keys (DeepSeek, OpenAI, or compatible providers)

2. **Create virtual environment**:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

4. **Install WhisperX manually (if automated script fails)**:
```bash
pip install whisperx torch torchaudio
```

5. **Initialize vector database**:
```bash
python -c "from core.rag.enhanced_rag_processor import VectorStore; VectorStore('data/vector_store/embeddings.db')"
```

6. **Download NLP models**:
```bash
python -m spacy download en_core_web_sm
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
```

## 🎯 Usage

### Basic Content Processing

1. **Upload Content**: Video files or paste transcripts
2. **Choose Processing Mode**: 
   - Enhanced (uses template system with PocketFlow)
   - Legacy (original processing)
3. **Configure Options**: Target audience, platform, content type
4. **Process**: AI analyzes and generates optimized content
5. **Export**: Download results in various formats

### Advanced Workflows

```python
from examples.youtube_repurposer_app import YouTubeRepurposerApp

# Initialize enhanced app
app = YouTubeRepurposerApp({
    'enable_rag': True,
    'enable_context_optimization': True,
    'enable_web_crawling': True
})

# Process content with multiple workflows
result = await app.repurpose_video(
    video_transcript="Your video content...",
    video_metadata={"title": "Original Title", "duration": 600}
)
```

## 🏗 Template System Architecture

### Core Components

1. **PocketFlow (`core/pocketflow.py`)**
   - Modular workflow system
   - Node-based processing (LLM, Function, Conditional)
   - Agent and RAG workflow patterns

2. **Enhanced RAG (`core/rag/enhanced_rag_processor.py`)**
   - Vector embeddings with SentenceTransformers
   - SQLite vector storage
   - Web crawling integration
   - Semantic search capabilities

3. **Context Engineering (`core/context_engineering/context_optimizer.py`)**
   - Advanced prompting strategies
   - Token optimization
   - Context window management
   - Confidence scoring

4. **Content Agents (`core/agents/content_analyzer.py`)**
   - Specialized content analysis
   - Engagement prediction
   - Platform-specific optimization

5. **Universal Template (`core/app_template.py`)**
   - Base class for content applications
   - Pre-configured workflows
   - Extensible architecture

### Creating New Applications

```python
from core.app_template import create_content_app

# Create a social media optimizer
social_app = create_content_app(
    app_type="social_optimizer",
    config={
        'platforms': ['instagram', 'tiktok', 'twitter'],
        'enable_rag': True,
        'enable_context_optimization': True
    }
)

# Create a script generator
script_app = create_content_app(
    app_type="script_generator",
    config={
        'script_types': ['educational', 'entertainment', 'commercial'],
        'enable_rag': True
    }
)
```

## 🔌 API Endpoints

### Enhanced Endpoints
- `POST /api/process_transcript` - Process with enhanced AI workflows
- `POST /api/generate_thumbnail` - AI-powered thumbnail optimization
- `GET /api/workflows` - List available workflows
- `POST /api/workflow/<name>` - Execute specific workflow
- `GET /api/progress/<task_id>` - Real-time progress tracking

### Legacy Endpoints (Maintained for compatibility)
- `POST /api/upload_video` - Upload video files
- `GET /api/health` - Application health check

## 🧪 Testing

```bash
# Run all tests
pytest tests/

# Run specific test categories
pytest tests/test_enhanced_app.py -v
pytest tests/test_workflows.py -v

# Run with coverage
pytest --cov=core tests/
```

## 📊 Monitoring and Logging

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Performance Metrics**: Processing times and success rates
- **Error Tracking**: Comprehensive error handling and reporting
- **Workflow Analytics**: Track workflow performance and usage

## 🔧 Configuration

### Environment Variables

```env
# API Keys
DEEPSEEK_API_KEY=your_key
OPENAI_API_KEY=your_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# WhisperX Configuration
WHISPERX_MODEL_SIZE=base  # tiny, base, small, medium, large-v2, large-v3
WHISPERX_DEVICE=auto      # auto, cpu, cuda, mps
WHISPERX_COMPUTE_TYPE=float16  # float16, int8, float32
WHISPERX_ENABLE_DIARIZATION=false  # Enable speaker identification
HF_TOKEN=your_huggingface_token  # Required for speaker diarization

# Database
DATABASE_URL=sqlite:///data/app.db
VECTOR_STORE_PATH=data/vector_store/embeddings.db

# Processing
MAX_UPLOAD_SIZE=500MB
PROCESSING_TIMEOUT=3600
CONCURRENT_JOBS=3

# Enhanced Features
ENABLE_RAG=true
ENABLE_CONTEXT_OPTIMIZATION=true
ENABLE_WEB_CRAWLING=false
CACHE_ENABLED=true

# Performance
MAX_CONTENT_LENGTH=10000
CONTEXT_WINDOW_SIZE=4000
MAX_TOKENS=2000

# Vector Store
EMBEDDING_MODEL=all-MiniLM-L6-v2
```

### WhisperX Model Sizes

| Model | Parameters | VRAM | Speed | Accuracy |
|-------|------------|------|----------|----------|
| tiny  | 39M        | ~1GB | Fastest | Good |
| base  | 74M        | ~1GB | Fast | Better |
| small | 244M       | ~2GB | Medium | Good |
| medium| 769M       | ~5GB | Slow | Better |
| large-v2| 1550M    | ~10GB| Slowest | Best |
| large-v3| 1550M    | ~10GB| Slowest | Best |

**Recommendations:**
- **CPU users**: Use `tiny` or `base` models
- **GPU users**: Use `small` or `medium` for best speed/accuracy balance
- **High accuracy needs**: Use `large-v2` or `large-v3`
- **Speaker identification**: Enable diarization with HuggingFace token

## 🚀 Deployment

### Docker Deployment

```dockerfile
# Dockerfile example
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN python setup_enhanced.py --skip-interactive

EXPOSE 5000
CMD ["python", "app.py"]
```

### Production Considerations

- Use production WSGI server (Gunicorn, uWSGI)
- Configure reverse proxy (Nginx)
- Set up monitoring (Prometheus, Grafana)
- Implement rate limiting
- Use external vector database (Pinecone, Weaviate)

## 📚 Examples and Cookbooks

See the `examples/` directory for:
- YouTube content repurposing
- Social media optimization
- Educational content generation
- Marketing copy creation
- SEO content optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- **Documentation**: See `TEMPLATE_USAGE.md` for detailed usage
- **Integration Guide**: See `INTEGRATION_PLAN.md` for implementation details
- **Issues**: Report bugs and feature requests via GitHub issues
- **Discussions**: Join community discussions for help and ideas