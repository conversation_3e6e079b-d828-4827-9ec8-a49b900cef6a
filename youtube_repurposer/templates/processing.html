<!DOCTYPE html>
<html>
<head>
    <title>Processing - YouTube Repurposer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; text-align: center; }
        .progress-container { margin: 40px 0; }
        .progress-bar { 
            height: 30px; 
            background: #f0f0f0; 
            border-radius: 15px; 
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: #4285f4;
            width: 0%;
            transition: width 0.5s ease;
        }
        .progress-text { margin-bottom: 10px; }
        .segment-progress { margin-top: 30px; text-align: left; }
        .segment-item { margin: 10px 0; padding: 10px; border: 1px solid #eee; border-radius: 4px; }
        .segment-status { display: inline-block; width: 100px; font-weight: bold; }
        .status-processing { color: #4285f4; }
        .status-completed { color: #34a853; }
        .status-failed { color: #ea4335; }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 4px solid #4285f4;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Processing Your Content</h1>
        <div class="progress-container">
            <div class="progress-text">Overall Progress: <span id="progress-percent">0</span>%</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-bar"></div>
            </div>
            <div id="progress-message">Starting processing...</div>
            <div class="spinner" id="spinner"></div>
        </div>
        
        <div class="segment-progress" id="segment-progress" style="display: none;">
            <h3>Segment Progress</h3>
            <div id="segment-list"></div>
        </div>
    </div>
    
    <script>
        const jobId = '{{ job_id }}';
        
        function updateProgress() {
            fetch(`/progress/${jobId}`)
                .then(response => response.json())
                .then(data => {
                    if (!data.status) {
                        // Job not found
                        document.getElementById('progress-message').textContent = 'Job not found';
                        return;
                    }
                    
                    // Update overall progress
                    const percent = Math.round((data.current / data.total) * 100);
                    document.getElementById('progress-percent').textContent = percent;
                    document.getElementById('progress-bar').style.width = `${percent}%`;
                    document.getElementById('progress-message').textContent = data.message;
                    
                    // Update segment progress
                    if (data.segments && Object.keys(data.segments).length > 0) {
                        document.getElementById('segment-progress').style.display = 'block';
                        const segmentList = document.getElementById('segment-list');
                        segmentList.innerHTML = '';
                        
                        Object.entries(data.segments).forEach(([index, segment]) => {
                            const segmentDiv = document.createElement('div');
                            segmentDiv.className = 'segment-item';
                            segmentDiv.innerHTML = `
                                <strong>Segment ${parseInt(index) + 1}:</strong>
                                <span class="segment-status status-${segment.status}">${segment.status}</span>
                                <div>${segment.message || ''}</div>
                            `;
                            segmentList.appendChild(segmentDiv);
                        });
                    }
                    
                    // Check if processing complete
                    if (data.status === 'completed') {
                        window.location.href = `/results/${jobId}`;
                    } else if (data.status === 'failed') {
                        document.getElementById('spinner').style.display = 'none';
                        document.getElementById('progress-message').innerHTML += 
                            '<p style="color:#ea4335">Processing failed. Please try again.</p>';
                    } else {
                        // Continue polling
                        setTimeout(updateProgress, 2000);
                    }
                });
        }
        
        // Start progress polling
        setTimeout(updateProgress, 1000);
    </script>
</body>
</html>