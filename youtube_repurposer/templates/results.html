<!DOCTYPE html>
<html>
<head>
    <title>Generated Shorts</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .shorts-container { display: flex; flex-wrap: wrap; gap: 20px; }
        .short-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
            width: 300px;
        }

        .short-card:hover {
            transform: translateY(-2px);
        }

        .no-video-card {
            border-left: 4px solid #f39c12;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .indicators {
            display: flex;
            flex-direction: column;
            gap: 5px;
            align-items: flex-end;
        }

        .video-indicator, .transcript-indicator, .verification-indicator, .theme-indicator {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .video-indicator {
            background: #d4edda;
            color: #155724;
        }

        .transcript-indicator {
            background: #fff3cd;
            color: #856404;
        }

        .verification-indicator.verified {
            background: #d1ecf1;
            color: #0c5460;
        }

        .verification-indicator.unverified {
            background: #f8d7da;
            color: #721c24;
        }

        .theme-indicator {
            background: #e2e3e5;
            color: #383d41;
        }

        .short-card h3 { margin-top: 0; }
        .short-card video { width: 100%; border-radius: 4px; }
        .thumbnail { width: 100%; height: 150px; object-fit: cover; border-radius: 4px; }
        .metadata { margin-top: 15px; font-size: 14px; }
        .section { margin-bottom: 10px; }
        .section-title { font-weight: bold; color: #555; }
        .actions { margin-top: 15px; display: flex; justify-content: space-between; }
        .btn { padding: 8px 15px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; }
        .btn:hover { background: #3367d6; }
        .btn-download { background: #34a853; }
        .btn-download:hover { background: #2d9249; }
        .transcript-section { margin-top: 15px; border-top: 1px solid #eee; padding-top: 15px; }
        .transcript-toggle { background: #f8f9fa; border: 1px solid #ddd; padding: 10px; border-radius: 4px; cursor: pointer; margin-bottom: 10px; }
        .transcript-toggle:hover { background: #e9ecef; }
        .transcript-content { background: #f8f9fa; padding: 15px; border-radius: 4px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto; font-size: 13px; line-height: 1.4; display: none; }
        .transcript-content.show { display: block; }
        .transcript-meta { font-size: 12px; color: #666; margin-bottom: 10px; }
        .transcript-icon { margin-right: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generated Shorts</h1>

        <!-- Verification Control Panel -->
        <div class="verification-panel">
            <h3>Verbatim Quote Verification</h3>
            <p>Ensure all generated content uses exact quotes from the original transcript to prevent hallucination.</p>
            <div class="verification-toggle">
                <label for="verification-toggle">Enable Verification:</label>
                <div class="toggle-switch" id="verification-toggle">
                    <div class="toggle-slider"></div>
                </div>
                <span id="verification-status">Loading...</span>
            </div>
        </div>

        <div class="shorts-container">
            {% for short in shorts %}
            <div class="short-card {% if not short.filename %}no-video-card{% endif %}">
                <div class="card-header">
                    <h3>{{ short.title }}</h3>
                    <div class="indicators">
                        {% if short.filename %}
                            <span class="video-indicator">📹 Video</span>
                        {% else %}
                            <span class="transcript-indicator">📄 Transcript Only</span>
                        {% endif %}
                        {% if short.verification_passed %}
                            <span class="verification-indicator verified">✅ Verified</span>
                        {% else %}
                            <span class="verification-indicator unverified">⚠️ Unverified</span>
                        {% endif %}
                        {% if short.theme %}
                            <span class="theme-indicator">🏷️ {{ short.theme }}</span>
                        {% endif %}
                    </div>
                </div>

                {% if short.thumbnail %}
                    <img class="thumbnail" src="{{ url_for('static_files', filename='thumbnails/' + short.thumbnail) }}" alt="Thumbnail">
                {% elif short.filename %}
                    <video class="thumbnail" controls>
                        <source src="{{ url_for('static_files', filename='shorts/' + short.filename) }}" type="video/mp4">
                    </video>
                {% else %}
                    <div class="thumbnail" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                        <p>No media available</p>
                    </div>
                {% endif %}

                <div class="metadata">
                    <div class="section">
                        <div class="section-title">Hook:</div>
                        <div>{{ short.hook }}</div>
                    </div>
                    <div class="section">
                        <div class="section-title">Context:</div>
                        <div>{{ short.context }}</div>
                    </div>
                    <div class="section">
                        <div class="section-title">Conclusion:</div>
                        <div>{{ short.conclusion }}</div>
                    </div>
                </div>

                <!-- Full Transcript Section -->
                <div class="transcript-section">
                    <div class="transcript-toggle" data-index="{{ loop.index0 }}">
                        <span class="transcript-icon">📝</span>
                        <strong>View Full Transcript</strong>
                        <span id="arrow-{{ loop.index0 }}" style="float: right;">▶</span>
                    </div>
                    <div id="transcript-{{ loop.index0 }}" class="transcript-content">
                        <div class="transcript-meta">
                            <strong>Complete Transcript Content</strong>
                            {% if short.start_ref and short.end_ref %}
                            <span style="float: right; color: #666;">⏱️ {{ short.start_ref }} - {{ short.end_ref }}</span>
                            {% endif %}
                        </div>
                        <div style="clear: both; margin-top: 10px;">
                            {% if short.full_transcript %}
                            <div style="margin-bottom: 15px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
                                <strong style="color: #495057; margin-bottom: 10px; display: block;">📄 Full Transcript:</strong>
                                <div style="white-space: pre-wrap; line-height: 1.6; color: #212529; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">{{ short.full_transcript }}</div>
                            </div>
                            {% endif %}

                            {% if short.supporting_quotes %}
                            <div style="margin-bottom: 15px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                                <strong style="color: #856404; margin-bottom: 10px; display: block;">💬 Key Quotes Used:</strong>
                                {% for quote in short.supporting_quotes %}
                                <div style="margin-bottom: 8px; padding: 8px; background: #fff; border-left: 3px solid #ffc107; border-radius: 3px;">
                                    <em>"{{ quote }}"</em>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <div style="margin-bottom: 15px; padding: 10px; background: #fff; border-left: 3px solid #4285f4; border-radius: 3px;">
                                <strong style="color: #4285f4;">🎣 Hook:</strong>
                                <p style="margin: 5px 0 0 0;">{{ short.hook or 'No hook content available' }}</p>
                            </div>
                            <div style="margin-bottom: 15px; padding: 10px; background: #fff; border-left: 3px solid #34a853; border-radius: 3px;">
                                <strong style="color: #34a853;">📖 Context:</strong>
                                <p style="margin: 5px 0 0 0;">{{ short.context or 'No context content available' }}</p>
                            </div>
                            <div style="margin-bottom: 10px; padding: 10px; background: #fff; border-left: 3px solid #ea4335; border-radius: 3px;">
                                <strong style="color: #ea4335;">🎯 Conclusion:</strong>
                                <p style="margin: 5px 0 0 0;">{{ short.conclusion or 'No conclusion content available' }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="actions">
                    {% if short.filename %}
                        <a href="{{ url_for('static_files', filename='shorts/' + short.filename) }}" download class="btn btn-download">Download Video</a>
                    {% endif %}
                    {% if short.thumbnail %}
                        <a href="{{ url_for('static_files', filename='thumbnails/' + short.thumbnail) }}" download class="btn">Download Thumbnail</a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <script>
        function toggleTranscript(index) {
            const content = document.getElementById(`transcript-${index}`);
            const arrow = document.getElementById(`arrow-${index}`);

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                arrow.textContent = '▶';
            } else {
                content.classList.add('show');
                arrow.textContent = '▼';
            }
        }

        // Verification toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const toggle = document.getElementById('verification-toggle');
            const status = document.getElementById('verification-status');

            // Load current verification status
            loadVerificationStatus();

            // Handle toggle click
            toggle.addEventListener('click', function() {
                const isActive = toggle.classList.contains('active');
                toggleVerification(!isActive);
            });

            function loadVerificationStatus() {
                fetch('/api/verification/status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.verification_enabled) {
                            toggle.classList.add('active');
                            status.textContent = 'Enabled';
                            status.style.color = '#28a745';
                        } else {
                            toggle.classList.remove('active');
                            status.textContent = 'Disabled';
                            status.style.color = '#dc3545';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading verification status:', error);
                        status.textContent = 'Error';
                        status.style.color = '#dc3545';
                    });
            }

            function toggleVerification(enabled) {
                status.textContent = 'Updating...';
                status.style.color = '#6c757d';

                fetch('/api/verification/toggle', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ enabled: enabled })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (enabled) {
                            toggle.classList.add('active');
                            status.textContent = 'Enabled';
                            status.style.color = '#28a745';
                        } else {
                            toggle.classList.remove('active');
                            status.textContent = 'Disabled';
                            status.style.color = '#dc3545';
                        }

                        // Show success message
                        showMessage(data.message, 'success');
                    } else {
                        showMessage('Failed to update verification setting', 'error');
                        loadVerificationStatus(); // Reload status
                    }
                })
                .catch(error => {
                    console.error('Error toggling verification:', error);
                    showMessage('Error updating verification setting', 'error');
                    loadVerificationStatus(); // Reload status
                });
            }

            function showMessage(message, type) {
                // Create and show a temporary message
                const messageDiv = document.createElement('div');
                messageDiv.textContent = message;
                const backgroundColor = type === 'success' ? '#28a745' : '#dc3545';
                messageDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 10px 15px;
                    border-radius: 4px;
                    color: white;
                    font-weight: 500;
                    z-index: 1000;
                    background: ${backgroundColor};
                `;

                document.body.appendChild(messageDiv);

                setTimeout(() => {
                    document.body.removeChild(messageDiv);
                }, 3000);
            }
        });
    </script>
</body>
</html>