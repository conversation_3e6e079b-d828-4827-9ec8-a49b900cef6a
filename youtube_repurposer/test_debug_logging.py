#!/usr/bin/env python3

from verbatim_quote_extractor import VerbatimQuoteExtractor
import json

def test_debug_logging():
    print("Starting debug logging test...")
    
    # Initialize extractor
    extractor = VerbatimQuoteExtractor()
    
    # Read the comprehensive transcript
    with open('test_comprehensive_transcript.vtt', 'r') as f:
        transcript = f.read()
    
    print(f"Loaded transcript with {len(transcript)} characters")
    
    # Extract verbatim quotes
    print("\n=== EXTRACTING VERBATIM QUOTES ===")
    quotes = extractor.extract_verbatim_quotes(transcript)
    print(f"Extracted {len(quotes)} quotes")
    
    # Create militant segments
    print("\n=== CREATING MILITANT SEGMENTS ===")
    segments = extractor.create_militant_segments(quotes)
    print(f"Created {len(segments)} segments")
    
    # Verify quotes authenticity
    print("\n=== VERIFYING QUOTES AUTHENTICITY ===")
    report = extractor.verify_quotes_authenticity(segments, transcript)
    
    print("\n=== FINAL RESULTS ===")
    print(f"Total segments created: {len(segments)}")
    print(f"Verification report:")
    print(json.dumps(report, indent=2))
    
    # Print segment details
    print("\n=== SEGMENT DETAILS ===")
    for i, segment in enumerate(segments):
        print(f"Segment {i+1}: {segment.title}")
        print(f"  Theme: {segment.theme}")
        print(f"  Total Duration: {segment.total_duration} seconds")
        print(f"  Timeline: {segment.estimated_start} - {segment.estimated_end}")
        print(f"  Urgency Score: {segment.urgency_score}")
        print(f"  Viral Potential: {segment.viral_potential}")
        print(f"  Hook Quote: {segment.hook_quote.text[:100]}...")
        print(f"  Supporting Quotes: {len(segment.supporting_quotes)}")
        print(f"  Conclusion Quote: {segment.conclusion_quote.text[:100]}...")
        print()

if __name__ == "__main__":
    test_debug_logging()