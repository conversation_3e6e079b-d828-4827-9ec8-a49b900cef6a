[mypy]
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False

# Handle missing imports for optional dependencies
[mypy-neo4j.*]
ignore_missing_imports = True

[mypy-sentence_transformers.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-aiohttp.*]
ignore_missing_imports = True

[mypy-bs4.*]
ignore_missing_imports = True
