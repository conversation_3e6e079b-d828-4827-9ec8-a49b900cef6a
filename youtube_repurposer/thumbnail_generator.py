import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def generate_thumbnail(video_path, output_path, title, hook):
    """Generate a thumbnail for a short video"""
    try:
        # Capture frame from video
        cap = cv2.VideoCapture(video_path)
        cap.set(cv2.CAP_PROP_POS_FRAMES, 30)  # Capture at 1 second (30fps)
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            return False
        
        # Convert to PIL image
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        img = Image.fromarray(frame)
        
        # Resize to thumbnail dimensions
        img = img.resize((1080, 1920), Image.LANCZOS)
        
        # Add overlay for text
        overlay = Image.new('RGBA', img.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        
        # Add gradient overlay
        for y in range(img.height):
            alpha = int(255 * (1 - y / img.height * 0.7))
            draw.line([(0, y), (img.width, y)], fill=(0, 0, 0, alpha))
        
        # Add title text
        title_font = ImageFont.truetype("arialbd.ttf", 72)
        draw.text((50, 100), title, font=title_font, fill="white")
        
        # Add hook text
        hook_font = ImageFont.truetype("arial.ttf", 48)
        draw.text((50, 200), hook, font=hook_font, fill="yellow")
        
        # Composite and save
        img = Image.alpha_composite(img.convert('RGBA'), overlay)
        img = img.convert('RGB')
        img.save(output_path)
        return True
    except Exception as e:
        print(f"Thumbnail generation failed: {e}")
        return False