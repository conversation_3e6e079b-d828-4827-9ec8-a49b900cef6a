#!/usr/bin/env python3

import asyncio
from deepseek_config import create_deepseek_client
from rag_processor import TranscriptProcessor

# Read the transcript file
with open('uploads/M<PERSON>_Adley_interview.txt', 'r', encoding='utf-8') as f:
    transcript_text = f.read()

print(f"Transcript length: {len(transcript_text)} characters")
print(f"First 200 characters: {transcript_text[:200]}...")
print("\n" + "="*50)

# Test DeepSeek client
deepseek_client = create_deepseek_client()
if deepseek_client:
    print("\n🔍 Testing DeepSeek R1 Analysis:")
    try:
        analysis_result = asyncio.run(deepseek_client.analyze_long_transcript(transcript_text, "comprehensive"))
        print(f"Analysis result keys: {list(analysis_result.keys()) if isinstance(analysis_result, dict) else 'Not a dict'}")
        
        if 'error' not in analysis_result:
            segments = analysis_result.get('top_segments', analysis_result.get('segments', []))
            print(f"\nRaw DeepSeek segments ({len(segments)} found):")
            for i, segment in enumerate(segments[:2]):  # Show first 2 segments
                print(f"\nSegment {i}:")
                for key, value in segment.items():
                    print(f"  {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
        else:
            print(f"DeepSeek error: {analysis_result['error']}")
    except Exception as e:
        print(f"DeepSeek failed: {e}")
else:
    print("❌ DeepSeek client not available")

print("\n" + "="*50)

# Test RAG processor
print("\n🔍 Testing RAG Processor:")
transcript_processor = TranscriptProcessor()
try:
    rag_segments = transcript_processor.process_transcript(transcript_text)
    print(f"\nRAG segments ({len(rag_segments)} found):")
    for i, segment in enumerate(rag_segments[:2]):  # Show first 2 segments
        print(f"\nSegment {i}:")
        for key, value in segment.items():
            print(f"  {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
except Exception as e:
    print(f"RAG processor failed: {e}")

print("\n" + "="*50)
print("\n✅ Debug complete!")