-- Supabase Database Setup for Crawl4AI RAG MCP
-- This script sets up the necessary tables and functions for the YouTube Repurposer RAG system

-- Enable the pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the crawled_pages table for storing web content and embeddings
CREATE TABLE IF NOT EXISTS crawled_pages (
    id BIGSERIAL PRIMARY KEY,
    url TEXT UNIQUE NOT NULL,
    title TEXT,
    content TEXT NOT NULL,
    markdown_content TEXT,
    summary TEXT,
    keywords TEXT[],
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    metadata JSONB DEFAULT '{}',
    source_domain TEXT,
    crawl_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    content_hash TEXT,
    content_type TEXT DEFAULT 'webpage',
    language TEXT DEFAULT 'en',
    word_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_crawled_pages_url ON crawled_pages(url);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_source_domain ON crawled_pages(source_domain);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_content_type ON crawled_pages(content_type);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_crawl_timestamp ON crawled_pages(crawl_timestamp);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_content_hash ON crawled_pages(content_hash);

-- Create vector similarity index for embeddings
CREATE INDEX IF NOT EXISTS idx_crawled_pages_embedding ON crawled_pages 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Create content chunks table for better RAG performance
CREATE TABLE IF NOT EXISTS content_chunks (
    id BIGSERIAL PRIMARY KEY,
    page_id BIGINT REFERENCES crawled_pages(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1536),
    metadata JSONB DEFAULT '{}',
    chunk_type TEXT DEFAULT 'paragraph', -- paragraph, header, code, list, etc.
    word_count INTEGER,
    character_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for content chunks
CREATE INDEX IF NOT EXISTS idx_content_chunks_page_id ON content_chunks(page_id);
CREATE INDEX IF NOT EXISTS idx_content_chunks_chunk_type ON content_chunks(chunk_type);
CREATE INDEX IF NOT EXISTS idx_content_chunks_embedding ON content_chunks 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Create crawl sessions table for tracking crawl operations
CREATE TABLE IF NOT EXISTS crawl_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID DEFAULT gen_random_uuid(),
    start_url TEXT NOT NULL,
    session_type TEXT DEFAULT 'single_page', -- single_page, recursive, sitemap
    status TEXT DEFAULT 'running', -- running, completed, failed
    pages_crawled INTEGER DEFAULT 0,
    pages_failed INTEGER DEFAULT 0,
    total_pages INTEGER,
    metadata JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- Create indexes for crawl sessions
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_session_id ON crawl_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_status ON crawl_sessions(status);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_started_at ON crawl_sessions(started_at);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updating updated_at
CREATE TRIGGER update_crawled_pages_updated_at 
    BEFORE UPDATE ON crawled_pages 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function for similarity search with optional source filtering
CREATE OR REPLACE FUNCTION similarity_search(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    source_filter text DEFAULT NULL
)
RETURNS TABLE (
    id bigint,
    url text,
    title text,
    content text,
    summary text,
    source_domain text,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.url,
        cp.title,
        cp.content,
        cp.summary,
        cp.source_domain,
        1 - (cp.embedding <=> query_embedding) as similarity
    FROM crawled_pages cp
    WHERE 
        (source_filter IS NULL OR cp.source_domain = source_filter)
        AND cp.embedding IS NOT NULL
        AND 1 - (cp.embedding <=> query_embedding) > match_threshold
    ORDER BY cp.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Function for chunk-based similarity search
CREATE OR REPLACE FUNCTION chunk_similarity_search(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 20,
    source_filter text DEFAULT NULL
)
RETURNS TABLE (
    chunk_id bigint,
    page_id bigint,
    url text,
    title text,
    chunk_content text,
    chunk_type text,
    source_domain text,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cc.id as chunk_id,
        cc.page_id,
        cp.url,
        cp.title,
        cc.content as chunk_content,
        cc.chunk_type,
        cp.source_domain,
        1 - (cc.embedding <=> query_embedding) as similarity
    FROM content_chunks cc
    JOIN crawled_pages cp ON cc.page_id = cp.id
    WHERE 
        (source_filter IS NULL OR cp.source_domain = source_filter)
        AND cc.embedding IS NOT NULL
        AND 1 - (cc.embedding <=> query_embedding) > match_threshold
    ORDER BY cc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Function to get available sources
CREATE OR REPLACE FUNCTION get_available_sources()
RETURNS TABLE (source_domain text, page_count bigint)
LANGUAGE sql
AS $$
    SELECT 
        source_domain,
        COUNT(*) as page_count
    FROM crawled_pages 
    WHERE source_domain IS NOT NULL
    GROUP BY source_domain
    ORDER BY page_count DESC;
$$;

-- Function to clean up old crawl data (optional)
CREATE OR REPLACE FUNCTION cleanup_old_crawls(days_old int DEFAULT 30)
RETURNS int
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count int;
BEGIN
    DELETE FROM crawled_pages 
    WHERE crawl_timestamp < NOW() - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- Create RLS (Row Level Security) policies if needed
-- Uncomment and modify these if you need user-specific access control

-- ALTER TABLE crawled_pages ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE content_chunks ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE crawl_sessions ENABLE ROW LEVEL SECURITY;

-- CREATE POLICY "Users can view all crawled pages" ON crawled_pages
--     FOR SELECT USING (true);

-- CREATE POLICY "Users can insert crawled pages" ON crawled_pages
--     FOR INSERT WITH CHECK (true);

-- Grant necessary permissions
-- GRANT USAGE ON SCHEMA public TO anon, authenticated;
-- GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
-- GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Insert some sample data for testing (optional)
-- INSERT INTO crawled_pages (url, title, content, source_domain, content_type)
-- VALUES 
--     ('https://example.com/test', 'Test Page', 'This is a test page for the RAG system.', 'example.com', 'webpage'),
--     ('https://docs.example.com/api', 'API Documentation', 'API documentation content here.', 'docs.example.com', 'documentation');

-- Display setup completion message
DO $$
BEGIN
    RAISE NOTICE 'Supabase database setup completed successfully!';
    RAISE NOTICE 'Tables created: crawled_pages, content_chunks, crawl_sessions';
    RAISE NOTICE 'Functions created: similarity_search, chunk_similarity_search, get_available_sources, cleanup_old_crawls';
    RAISE NOTICE 'Indexes created for optimal performance';
    RAISE NOTICE 'Ready for Crawl4AI RAG MCP integration!';
END $$;