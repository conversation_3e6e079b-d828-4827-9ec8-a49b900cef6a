# Core dependencies
Flask==2.3.3
Werkzeug==2.3.7
requests==2.31.0
Pillow==10.0.1
numpy==1.24.3
pandas==2.0.3

# DeepSeek R1 API client
openai>=1.0.0  # DeepSeek uses OpenAI-compatible API
httpx>=0.24.0
aiohttp>=3.8.0

# WhisperX and dependencies
whisperx>=3.1.1
torch>=2.0.0
torchaudio>=2.0.0
faster-whisper>=0.9.0
pyannote.audio>=3.1.0
huggingface-hub>=0.16.0
transformers>=4.30.0

# Enhanced RAG dependencies
sentence-transformers>=2.2.0
scikit-learn>=1.3.0
nltk>=3.8.0

# Crawl4AI RAG MCP Integration (Python 3.11 compatible)
crawl4ai>=0.2.0
supabase>=1.0.0
psycopg2-binary>=2.8.0
neo4j>=4.4.0
playwright>=1.30.0
# pgvector>=0.2.0  # May require specific setup
rank-bm25>=0.2.0
# cross-encoder>=1.2.0  # Not compatible with Python 3.11
sentence-transformers>=2.2.0  # Alternative for cross-encoder functionality

# Environment management
python-dotenv>=1.0.0