{"data_mtime": 1752069007, "dep_lines": [16, 1, 2, 3, 15, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "sys", "_blake2", "_hashlib", "_typeshed", "typing", "builtins", "_frozen_importlib", "abc", "types", "typing_extensions"], "hash": "291d4286754378b8ed246a100f38872ee4e2e30c", "id": "<PERSON><PERSON><PERSON>", "ignore_all": true, "interface_hash": "485a644ad1bac67b88e3a05b995daee730805dd2", "mtime": 1752067884, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/deepseek-engineer/youtube_repurposer/venv/lib/python3.11/site-packages/mypy/typeshed/stdlib/hashlib.pyi", "plugin_data": null, "size": 2984, "suppressed": [], "version_id": "1.16.1"}