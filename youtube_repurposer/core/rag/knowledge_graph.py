#!/usr/bin/env python3
"""
Knowledge Graph Integration for YouTube Repurposer RAG System

This module provides knowledge graph functionality using Neo4j to enhance
RAG capabilities with relationship-based information retrieval.
"""

import os
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime
import logging
from pathlib import Path
import hashlib

# Neo4j imports
try:
    from neo4j import GraphDatabase, Driver, Session  # type: ignore
    from neo4j.exceptions import ServiceUnavailable, AuthError  # type: ignore
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    GraphDatabase = None  # type: ignore
    Driver = None  # type: ignore
    Session = None  # type: ignore

# Local imports
try:
    from .enhanced_rag_processor import Document, RetrievalResult
except ImportError:
    # Fallback for when enhanced_rag_processor is not available
    Document = None  # type: ignore
    RetrievalResult = None  # type: ignore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Entity:
    """Represents an entity in the knowledge graph."""
    id: str
    name: str
    type: str
    properties: Dict[str, Any]
    source_url: Optional[str] = None
    confidence: float = 1.0
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if not self.id:
            self.id = self._generate_id()
    
    def _generate_id(self) -> str:
        """Generate a unique ID for the entity."""
        content = f"{self.name}_{self.type}_{self.source_url or ''}"
        return hashlib.md5(content.encode()).hexdigest()[:12]

@dataclass
class Relationship:
    """Represents a relationship between entities."""
    id: str
    source_entity_id: str
    target_entity_id: str
    relationship_type: str
    properties: Dict[str, Any]
    confidence: float = 1.0
    source_url: Optional[str] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if not self.id:
            self.id = self._generate_id()
    
    def _generate_id(self) -> str:
        """Generate a unique ID for the relationship."""
        content = f"{self.source_entity_id}_{self.relationship_type}_{self.target_entity_id}"
        return hashlib.md5(content.encode()).hexdigest()[:12]

@dataclass
class GraphQuery:
    """Represents a graph query with results."""
    query: str
    cypher_query: str
    entities: List[Entity]
    relationships: List[Relationship]
    execution_time: float
    total_results: int

class KnowledgeGraph:
    """Knowledge Graph implementation using Neo4j."""
    
    def __init__(self, uri: Optional[str] = None, user: Optional[str] = None, password: Optional[str] = None):
        """Initialize the Knowledge Graph."""
        self.driver: Optional[Driver] = None
        self.connected = False
        
        # Check if Neo4j is available
        if not NEO4J_AVAILABLE:
            logger.warning("Neo4j driver not available. Install with: pip install neo4j")
            return
        
        # Get connection parameters
        self.uri = uri or os.getenv("NEO4J_URI", "bolt://localhost:7687")
        self.user = user or os.getenv("NEO4J_USER", "neo4j")
        self.password = password or os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            logger.warning("Neo4j password not provided. Knowledge graph features disabled.")
            return
        
        # Initialize connection
        self._connect()
    
    def _connect(self):
        """Establish connection to Neo4j database."""
        try:
            self.driver = GraphDatabase.driver(
                self.uri, 
                auth=(self.user, self.password)
            )
            
            # Test connection
            if self.driver is not None:
                with self.driver.session() as session:
                    session.run("RETURN 1")
            
            self.connected = True
            logger.info(f"Connected to Neo4j at {self.uri}")
            
            # Initialize schema
            self._initialize_schema()
            
        except (ServiceUnavailable, AuthError) as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            self.connected = False
        except Exception as e:
            logger.error(f"Unexpected error connecting to Neo4j: {e}")
            self.connected = False
    
    def _initialize_schema(self):
        """Initialize Neo4j schema with constraints and indexes."""
        if not self.connected:
            return
        
        schema_queries = [
            # Constraints
            "CREATE CONSTRAINT entity_id_unique IF NOT EXISTS FOR (e:Entity) REQUIRE e.id IS UNIQUE",
            "CREATE CONSTRAINT document_id_unique IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE",
            
            # Indexes
            "CREATE INDEX entity_name_index IF NOT EXISTS FOR (e:Entity) ON (e.name)",
            "CREATE INDEX entity_type_index IF NOT EXISTS FOR (e:Entity) ON (e.type)",
            "CREATE INDEX document_url_index IF NOT EXISTS FOR (d:Document) ON (d.url)",
            "CREATE INDEX relationship_type_index IF NOT EXISTS FOR ()-[r]-() ON (r.type)"
        ]
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    for query in schema_queries:
                        try:
                            session.run(query)
                        except Exception as e:
                            # Constraint/index might already exist
                            logger.debug(f"Schema query failed (might already exist): {e}")
            
            logger.info("Neo4j schema initialized")
            
        except Exception as e:
            logger.error(f"Error initializing schema: {e}")
    
    def add_entity(self, entity: Entity) -> bool:
        """Add an entity to the knowledge graph."""
        if not self.connected:
            return False
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    query = """
                MERGE (e:Entity {id: $id})
                SET e.name = $name,
                    e.type = $type,
                    e.properties = $properties,
                    e.source_url = $source_url,
                    e.confidence = $confidence,
                    e.created_at = $created_at,
                    e.updated_at = datetime()
                RETURN e
                """
                    
                    result = session.run(query, {
                        "id": entity.id,
                        "name": entity.name,
                        "type": entity.type,
                        "properties": json.dumps(entity.properties),
                        "source_url": entity.source_url,
                        "confidence": entity.confidence,
                        "created_at": entity.created_at.isoformat()
                    })
                    
                    return len(list(result)) > 0
                
        except Exception as e:
            logger.error(f"Error adding entity {entity.id}: {e}")
            return False
    
    def add_relationship(self, relationship: Relationship) -> bool:
        """Add a relationship to the knowledge graph."""
        if not self.connected:
            return False
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    query = """
                MATCH (source:Entity {id: $source_id})
                MATCH (target:Entity {id: $target_id})
                MERGE (source)-[r:RELATES {type: $rel_type, id: $rel_id}]->(target)
                SET r.properties = $properties,
                    r.confidence = $confidence,
                    r.source_url = $source_url,
                    r.created_at = $created_at,
                    r.updated_at = datetime()
                RETURN r
                """
                    
                    result = session.run(query, {
                        "source_id": relationship.source_entity_id,
                        "target_id": relationship.target_entity_id,
                        "rel_type": relationship.relationship_type,
                        "rel_id": relationship.id,
                        "properties": json.dumps(relationship.properties),
                        "confidence": relationship.confidence,
                        "source_url": relationship.source_url,
                        "created_at": relationship.created_at.isoformat()
                    })
                    
                    return len(list(result)) > 0
                
        except Exception as e:
            logger.error(f"Error adding relationship {relationship.id}: {e}")
            return False
    
    def add_document_entities(self, document: Document, entities: List[Entity], 
                            relationships: Optional[List[Relationship]] = None) -> bool:
        """Add a document and its extracted entities to the knowledge graph."""
        if not self.connected:
            return False
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    # Add document node
                    doc_query = """
                MERGE (d:Document {id: $doc_id})
                SET d.url = $url,
                    d.title = $title,
                    d.content_preview = $content_preview,
                    d.metadata = $metadata,
                    d.created_at = $created_at,
                    d.updated_at = datetime()
                RETURN d
                """
                    
                    session.run(doc_query, {
                        "doc_id": document.id,
                        "url": document.source_url,
                        "title": document.metadata.get("title", ""),
                        "content_preview": document.content[:500] if document.content else "",
                        "metadata": json.dumps(document.metadata),
                        "created_at": datetime.now().isoformat()
                    })
                    
                    # Add entities and link to document
                    for entity in entities:
                        self.add_entity(entity)
                        
                        # Link entity to document
                        link_query = """
                    MATCH (d:Document {id: $doc_id})
                    MATCH (e:Entity {id: $entity_id})
                    MERGE (d)-[r:MENTIONS]->(e)
                    SET r.created_at = datetime()
                    """
                        
                        session.run(link_query, {
                            "doc_id": document.id,
                            "entity_id": entity.id
                        })
                    
                    # Add relationships if provided
                    if relationships:
                        for relationship in relationships:
                            self.add_relationship(relationship)
                    
                    return True
                
        except Exception as e:
            logger.error(f"Error adding document entities: {e}")
            return False
    
    def find_related_entities(self, entity_name: str, max_depth: int = 2, 
                            limit: int = 10) -> List[Entity]:
        """Find entities related to a given entity."""
        if not self.connected:
            return []
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    query = f"""
                MATCH (start:Entity {{name: $entity_name}})
                MATCH (start)-[*1..{max_depth}]-(related:Entity)
                WHERE related <> start
                RETURN DISTINCT related
                ORDER BY related.confidence DESC
                LIMIT $limit
                """
                
                result = session.run(query, {
                    "entity_name": entity_name,
                    "limit": limit
                })
                
                entities = []
                for record in result:
                    node = record["related"]
                    entity = Entity(
                        id=node["id"],
                        name=node["name"],
                        type=node["type"],
                        properties=json.loads(node.get("properties", "{}")),
                        source_url=node.get("source_url"),
                        confidence=node.get("confidence", 1.0)
                    )
                    entities.append(entity)
                
                return entities
                
        except Exception as e:
            logger.error(f"Error finding related entities: {e}")
            return []
    
    def search_entities(self, query: str, entity_types: Optional[List[str]] = None, 
                       limit: int = 10) -> List[Entity]:
        """Search for entities by name or properties."""
        if not self.connected:
            return []
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    # Build query with optional type filter
                    type_filter = ""
                    if entity_types:
                        type_filter = f"AND e.type IN {entity_types}"
                    
                    cypher_query = f"""
                MATCH (e:Entity)
                WHERE (e.name CONTAINS $query OR 
                       any(key IN keys(e.properties) WHERE e.properties[key] CONTAINS $query))
                {type_filter}
                RETURN e
                ORDER BY e.confidence DESC
                LIMIT $limit
                """
                    
                    result = session.run(cypher_query, {
                        "query": query,
                        "limit": limit
                    })
                    
                    entities = []
                    for record in result:
                        node = record["e"]
                        entity = Entity(
                            id=node["id"],
                            name=node["name"],
                            type=node["type"],
                            properties=json.loads(node.get("properties", "{}")),
                            source_url=node.get("source_url"),
                            confidence=node.get("confidence", 1.0)
                        )
                        entities.append(entity)
                
                return entities
                
        except Exception as e:
            logger.error(f"Error searching entities: {e}")
            return []
    
    def get_entity_context(self, entity_id: str, context_depth: int = 1) -> Dict[str, Any]:
        """Get contextual information about an entity."""
        if not self.connected:
            return {}
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    query = """
                MATCH (e:Entity {id: $entity_id})
                OPTIONAL MATCH (e)-[r1]->(connected1:Entity)
                OPTIONAL MATCH (e)<-[r2]-(connected2:Entity)
                OPTIONAL MATCH (d:Document)-[:MENTIONS]->(e)
                RETURN e, 
                       collect(DISTINCT {entity: connected1, relationship: r1, direction: 'outgoing'}) as outgoing,
                       collect(DISTINCT {entity: connected2, relationship: r2, direction: 'incoming'}) as incoming,
                       collect(DISTINCT d) as documents
                """
                
                result = session.run(query, {"entity_id": entity_id})
                record = result.single()
                
                if not record:
                    return {}
                
                # Parse entity
                entity_node = record["e"]
                entity = Entity(
                    id=entity_node["id"],
                    name=entity_node["name"],
                    type=entity_node["type"],
                    properties=json.loads(entity_node.get("properties", "{}")),
                    source_url=entity_node.get("source_url"),
                    confidence=entity_node.get("confidence", 1.0)
                )
                
                # Parse connections
                connections: Dict[str, List[Dict[str, Any]]] = {
                    "outgoing": [],
                    "incoming": []
                }
                
                for conn in record["outgoing"]:
                    if conn["entity"]:
                        connections["outgoing"].append({
                            "entity": conn["entity"]["name"],
                            "relationship": conn["relationship"]["type"],
                            "confidence": conn["relationship"].get("confidence", 1.0)
                        })
                
                for conn in record["incoming"]:
                    if conn["entity"]:
                        connections["incoming"].append({
                            "entity": conn["entity"]["name"],
                            "relationship": conn["relationship"]["type"],
                            "confidence": conn["relationship"].get("confidence", 1.0)
                        })
                
                # Parse documents
                documents = []
                for doc in record["documents"]:
                    if doc:
                        documents.append({
                            "id": doc["id"],
                            "url": doc.get("url"),
                            "title": doc.get("title")
                        })
                
                return {
                    "entity": asdict(entity),
                    "connections": connections,
                    "documents": documents,
                    "total_connections": len(connections["outgoing"]) + len(connections["incoming"]),
                    "total_documents": len(documents)
                }
                
        except Exception as e:
            logger.error(f"Error getting entity context: {e}")
            return {}
    
    def execute_cypher_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a custom Cypher query."""
        if not self.connected:
            return []
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    result = session.run(query, parameters or {})
                    return [dict(record) for record in result]
                
        except Exception as e:
            logger.error(f"Error executing Cypher query: {e}")
            return []
    
    def get_graph_statistics(self) -> Dict[str, Any]:
        """Get statistics about the knowledge graph."""
        if not self.connected:
            return {}
        
        try:
            if self.driver is not None:
                with self.driver.session() as session:
                    stats_query = """
                MATCH (e:Entity) 
                WITH count(e) as entity_count
                MATCH (d:Document)
                WITH entity_count, count(d) as document_count
                MATCH ()-[r]->()
                WITH entity_count, document_count, count(r) as relationship_count
                MATCH (e:Entity)
                WITH entity_count, document_count, relationship_count, 
                     collect(DISTINCT e.type) as entity_types
                RETURN entity_count, document_count, relationship_count, entity_types
                """
                
                result = session.run(stats_query)
                record = result.single()
                
                if record:
                    return {
                        "total_entities": record["entity_count"],
                        "total_documents": record["document_count"],
                        "total_relationships": record["relationship_count"],
                        "entity_types": record["entity_types"],
                        "connected": True
                    }
                else:
                    return {"connected": True, "total_entities": 0, "total_documents": 0, "total_relationships": 0}
                    
        except Exception as e:
            logger.error(f"Error getting graph statistics: {e}")
            return {"connected": False, "error": str(e)}
    
    def close(self):
        """Close the Neo4j connection."""
        if self.driver:
            self.driver.close()
            self.connected = False
            logger.info("Neo4j connection closed")

# Factory function
def create_knowledge_graph(uri: Optional[str] = None, user: Optional[str] = None, password: Optional[str] = None) -> KnowledgeGraph:
    """Create and configure a Knowledge Graph instance."""
    return KnowledgeGraph(uri, user, password)

# Example usage
if __name__ == "__main__":
    # Create knowledge graph
    kg = create_knowledge_graph()
    
    if kg.connected:
        # Example: Add an entity
        entity = Entity(
            id="",
            name="Machine Learning",
            type="Concept",
            properties={"description": "A subset of artificial intelligence"},
            source_url="https://example.com"
        )
        
        success = kg.add_entity(entity)
        print(f"Entity added: {success}")
        
        # Example: Search entities
        results = kg.search_entities("machine learning")
        print(f"Found {len(results)} entities")
        
        # Get statistics
        stats = kg.get_graph_statistics()
        print(f"Graph statistics: {stats}")
        
        # Close connection
        kg.close()
    else:
        print("Knowledge graph not available")