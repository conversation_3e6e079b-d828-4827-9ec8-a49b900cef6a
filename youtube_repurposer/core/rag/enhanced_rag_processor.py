# Enhanced RAG Processor with Crawl4AI Integration
# Implements advanced RAG patterns with web crawling capabilities

import json
import asyncio
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import sqlite3
import os
from pathlib import Path

# Vector similarity imports (will be installed as needed)
try:
    import numpy as np
    from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    np = None
    SentenceTransformer = None

@dataclass
class Document:
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    created_at: datetime = None
    source_url: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.id is None:
            self.id = self._generate_id()
    
    def _generate_id(self) -> str:
        content_hash = hashlib.md5(self.content.encode()).hexdigest()
        return f"doc_{content_hash[:12]}"

@dataclass
class RetrievalResult:
    documents: List[Document]
    query: str
    scores: List[float]
    total_results: int
    retrieval_time: float

class VectorStore:
    """Simple vector store for document embeddings"""
    
    def __init__(self, db_path: str = "vector_store.db"):
        self.db_path = db_path
        self.model = None
        self._init_db()
        
        if EMBEDDINGS_AVAILABLE:
            self.model = SentenceTransformer('all-MiniLM-L6-v2')
    
    def _init_db(self):
        """Initialize SQLite database for document storage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                metadata TEXT,
                embedding BLOB,
                created_at TIMESTAMP,
                source_url TEXT
            )
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_created_at ON documents(created_at)
        """)
        
        conn.commit()
        conn.close()
    
    def add_document(self, document: Document) -> bool:
        """Add a document to the vector store"""
        try:
            # Generate embedding if model is available
            if self.model and document.embedding is None:
                embedding = self.model.encode(document.content)
                document.embedding = embedding.tolist()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            embedding_blob = None
            if document.embedding:
                embedding_blob = json.dumps(document.embedding).encode()
            
            cursor.execute("""
                INSERT OR REPLACE INTO documents 
                (id, content, metadata, embedding, created_at, source_url)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                document.id,
                document.content,
                json.dumps(document.metadata),
                embedding_blob,
                document.created_at.isoformat(),
                document.source_url
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"Error adding document: {e}")
            return False
    
    def search(self, query: str, top_k: int = 5) -> List[Tuple[Document, float]]:
        """Search for similar documents"""
        if not self.model:
            # Fallback to keyword search
            return self._keyword_search(query, top_k)
        
        # Generate query embedding
        query_embedding = self.model.encode(query)
        
        # Retrieve all documents with embeddings
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, content, metadata, embedding, created_at, source_url
            FROM documents 
            WHERE embedding IS NOT NULL
        """)
        
        results = []
        for row in cursor.fetchall():
            doc_id, content, metadata_str, embedding_blob, created_at_str, source_url = row
            
            # Reconstruct document
            metadata = json.loads(metadata_str) if metadata_str else {}
            embedding = json.loads(embedding_blob.decode()) if embedding_blob else None
            created_at = datetime.fromisoformat(created_at_str)
            
            document = Document(
                id=doc_id,
                content=content,
                metadata=metadata,
                embedding=embedding,
                created_at=created_at,
                source_url=source_url
            )
            
            # Calculate similarity
            if embedding:
                similarity = self._cosine_similarity(query_embedding, np.array(embedding))
                results.append((document, similarity))
        
        conn.close()
        
        # Sort by similarity and return top_k
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:top_k]
    
    def _keyword_search(self, query: str, top_k: int = 5) -> List[Tuple[Document, float]]:
        """Fallback keyword-based search"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Simple keyword matching
        keywords = query.lower().split()
        like_conditions = [f"LOWER(content) LIKE '%{keyword}%'" for keyword in keywords]
        where_clause = " OR ".join(like_conditions)
        
        cursor.execute(f"""
            SELECT id, content, metadata, embedding, created_at, source_url
            FROM documents 
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT ?
        """, (top_k,))
        
        results = []
        for row in cursor.fetchall():
            doc_id, content, metadata_str, embedding_blob, created_at_str, source_url = row
            
            metadata = json.loads(metadata_str) if metadata_str else {}
            created_at = datetime.fromisoformat(created_at_str)
            
            document = Document(
                id=doc_id,
                content=content,
                metadata=metadata,
                created_at=created_at,
                source_url=source_url
            )
            
            # Simple relevance score based on keyword matches
            score = sum(1 for keyword in keywords if keyword in content.lower()) / len(keywords)
            results.append((document, score))
        
        conn.close()
        return results
    
    def _cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

class WebCrawler:
    """Web crawler for gathering external context"""
    
    def __init__(self):
        self.session = None
    
    async def crawl_url(self, url: str) -> Optional[Document]:
        """Crawl a single URL and return a document"""
        try:
            import aiohttp
            from bs4 import BeautifulSoup
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Extract text content
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    text = soup.get_text()
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    content = ' '.join(chunk for chunk in chunks if chunk)
                    
                    # Extract metadata
                    title = soup.find('title')
                    title_text = title.get_text() if title else url
                    
                    metadata = {
                        'title': title_text,
                        'url': url,
                        'crawled_at': datetime.now().isoformat(),
                        'content_length': len(content)
                    }
                    
                    return Document(
                        id=None,  # Will be auto-generated
                        content=content[:10000],  # Limit content size
                        metadata=metadata,
                        source_url=url
                    )
                    
        except Exception as e:
            print(f"Error crawling {url}: {e}")
            return None
    
    async def search_and_crawl(self, query: str, num_results: int = 3) -> List[Document]:
        """Search for URLs and crawl them"""
        # This is a simplified implementation
        # In a real scenario, you'd integrate with search APIs
        
        # For now, return some example URLs related to content creation
        example_urls = [
            "https://blog.hootsuite.com/social-media-content-creation/",
            "https://contentmarketinginstitute.com/articles/content-creation-process/",
            "https://sproutsocial.com/insights/social-media-content-creation/"
        ]
        
        documents = []
        for url in example_urls[:num_results]:
            doc = await self.crawl_url(url)
            if doc:
                documents.append(doc)
        
        return documents
    
    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()

class EnhancedRAGProcessor:
    """Enhanced RAG processor with web crawling and advanced retrieval"""
    
    def __init__(self, llm_client, vector_store_path: str = "rag_vector_store.db"):
        self.llm_client = llm_client
        self.vector_store = VectorStore(vector_store_path)
        self.web_crawler = WebCrawler()
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = timedelta(hours=1)
    
    async def retrieve_context(self, query: str, include_web: bool = True, top_k: int = 5) -> Dict[str, Any]:
        """Retrieve relevant context for a query"""
        start_time = datetime.now()
        
        # Check cache first
        cache_key = hashlib.md5(f"{query}_{include_web}_{top_k}".encode()).hexdigest()
        if cache_key in self.cache:
            cached_result, cached_time = self.cache[cache_key]
            if datetime.now() - cached_time < self.cache_ttl:
                return cached_result
        
        # Search local vector store
        local_results = self.vector_store.search(query, top_k)
        
        # Optionally crawl web for additional context
        web_documents = []
        if include_web:
            try:
                web_documents = await self.web_crawler.search_and_crawl(query, 2)
                # Add web documents to vector store for future use
                for doc in web_documents:
                    self.vector_store.add_document(doc)
            except Exception as e:
                print(f"Web crawling failed: {e}")
        
        # Combine results
        all_documents = [doc for doc, score in local_results] + web_documents
        all_scores = [score for doc, score in local_results] + [0.5] * len(web_documents)
        
        retrieval_time = (datetime.now() - start_time).total_seconds()
        
        result = {
            'documents': all_documents,
            'query': query,
            'scores': all_scores,
            'total_results': len(all_documents),
            'retrieval_time': retrieval_time,
            'sources': {
                'local': len(local_results),
                'web': len(web_documents)
            }
        }
        
        # Cache the result
        self.cache[cache_key] = (result, datetime.now())
        
        return result
    
    async def generate_enhanced_response(self, query: str, context: Dict[str, Any]) -> str:
        """Generate a response using retrieved context"""
        documents = context.get('documents', [])
        
        if not documents:
            return await self._generate_fallback_response(query)
        
        # Prepare context for LLM
        context_text = "\n\n".join([
            f"Source: {doc.metadata.get('title', 'Unknown')}\n{doc.content[:1000]}"
            for doc in documents[:3]  # Limit to top 3 documents
        ])
        
        prompt = f"""
You are an expert content analyst with access to relevant background information.

User Query: {query}

Relevant Context:
{context_text}

Based on the provided context and your expertise, provide a comprehensive and accurate response to the user's query. If the context doesn't fully address the query, acknowledge this and provide the best guidance you can based on available information.

Response:
"""
        
        try:
            # Check if using DeepSeek client or OpenAI client
            if hasattr(self.llm_client, 'analyze_content'):
                # Using DeepSeek client
                response_data = await self.llm_client.analyze_content(prompt, "rag_response")
                if 'error' not in response_data:
                    return response_data.get('response', response_data.get('analysis', 'No response generated'))
                else:
                    return await self._generate_fallback_response(query)
            else:
                # Using OpenAI client
                response = await self.llm_client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=1000,
                    temperature=0.7
                )
                
                return response.choices[0].message.content
            
        except Exception as e:
            print(f"LLM generation failed: {e}")
            return await self._generate_fallback_response(query)
    
    async def _generate_fallback_response(self, query: str) -> str:
        """Generate a fallback response when context retrieval fails"""
        return f"I understand you're asking about: {query}. While I don't have specific context available right now, I can provide general guidance based on content creation best practices."
    
    def add_document(self, content: str, metadata: Dict[str, Any] = None, source_url: str = None) -> bool:
        """Add a document to the knowledge base"""
        document = Document(
            id=None,  # Auto-generated
            content=content,
            metadata=metadata or {},
            source_url=source_url
        )
        
        return self.vector_store.add_document(document)
    
    def add_transcript(self, transcript: str, video_metadata: Dict[str, Any] = None) -> bool:
        """Add a video transcript to the knowledge base"""
        metadata = video_metadata or {}
        metadata.update({
            'content_type': 'video_transcript',
            'added_at': datetime.now().isoformat()
        })
        
        return self.add_document(transcript, metadata)
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base"""
        conn = sqlite3.connect(self.vector_store.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM documents")
        total_docs = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM documents 
            WHERE created_at > datetime('now', '-7 days')
        """)
        recent_docs = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM documents 
            WHERE source_url IS NOT NULL
        """)
        web_docs = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_documents': total_docs,
            'recent_documents': recent_docs,
            'web_documents': web_docs,
            'cache_size': len(self.cache),
            'embeddings_available': EMBEDDINGS_AVAILABLE
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.web_crawler.close()
        self.cache.clear()

# Example usage and testing
if __name__ == "__main__":
    async def test_rag_processor():
        # This would normally use your actual LLM client
        class MockLLMClient:
            class Chat:
                class Completions:
                    async def create(self, **kwargs):
                        class MockResponse:
                            class Choice:
                                class Message:
                                    content = "This is a mock response based on the provided context."
                                message = Message()
                            choices = [Choice()]
                        return MockResponse()
                completions = Completions()
            chat = Chat()
        
        rag = EnhancedRAGProcessor(MockLLMClient())
        
        # Add some test documents
        rag.add_document(
            "Content creation involves planning, producing, and distributing valuable content to engage audiences.",
            {'topic': 'content_creation', 'type': 'definition'}
        )
        
        # Test retrieval
        context = await rag.retrieve_context("How to create engaging content?", include_web=False)
        print(f"Retrieved {context['total_results']} documents")
        
        # Test enhanced response
        response = await rag.generate_enhanced_response("How to create engaging content?", context)
        print(f"Generated response: {response}")
        
        # Get stats
        stats = await rag.get_stats()
        print(f"RAG Stats: {stats}")
        
        await rag.cleanup()
    
    # Run test
    # asyncio.run(test_rag_processor())
    pass