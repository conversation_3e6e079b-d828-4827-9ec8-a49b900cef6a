#!/usr/bin/env python3
"""
Crawl4AI Setup Script for YouTube Repurposer

This script sets up Crawl4AI with all necessary dependencies and configurations
for integration with the YouTube Repurposer RAG system.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr.strip()}")
        return False

def check_python_version():
    """Check if Python 3.8+ is available (relaxed requirement)."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required. Current version: {version.major}.{version.minor}")
        print("Please install Python 3.8+ to use crawl4ai-rag-mcp")
        return False
    
    if version.minor < 12:
        print(f"⚠️  Python {version.major}.{version.minor} detected (3.12+ recommended)")
        print("Some features may have limited compatibility")
    else:
        print(f"✅ Python {version.major}.{version.minor} detected")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    # Install main requirements
    if not run_command("pip install -r requirements.txt", "Installing main requirements"):
        return False
    
    return True

def setup_crawl4ai():
    """Run Crawl4AI post-installation setup."""
    print("\n🕷️ Setting up Crawl4AI...")
    
    # Run post-installation setup
    if not run_command("crawl4ai-setup", "Running Crawl4AI post-installation setup"):
        return False
    
    # Verify installation
    if not run_command("crawl4ai-doctor", "Verifying Crawl4AI installation"):
        print("⚠️ Crawl4AI verification failed, but installation may still work")
    
    return True

def create_directories():
    """Create necessary directories for the project."""
    print("\n📁 Creating project directories...")
    
    directories = [
        "data/crawl4ai_cache",
        "data/supabase_cache",
        "data/neo4j_data",
        "logs/crawl4ai"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def check_env_file():
    """Check if .env file exists and provide guidance."""
    print("\n🔧 Checking environment configuration...")
    
    if not os.path.exists(".env"):
        print("⚠️ .env file not found")
        print("📋 Please copy .env.example to .env and configure the following:")
        print("   - OPENAI_API_KEY (for embeddings)")
        print("   - SUPABASE_URL and SUPABASE_SERVICE_KEY (for vector storage)")
        print("   - NEO4J_* variables (optional, for knowledge graph)")
        print("\n💡 Run: cp .env.example .env")
        return False
    else:
        print("✅ .env file found")
        return True

def main():
    """Main setup function."""
    print("🚀 Starting Crawl4AI RAG MCP Setup for YouTube Repurposer")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Setup Crawl4AI
    if not setup_crawl4ai():
        print("❌ Failed to setup Crawl4AI")
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("❌ Failed to create directories")
        sys.exit(1)
    
    # Check environment file
    env_configured = check_env_file()
    
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    
    if not env_configured:
        print("\n⚠️ Next steps:")
        print("1. Copy .env.example to .env: cp .env.example .env")
        print("2. Configure your API keys and database URLs in .env")
        print("3. Set up Supabase database with pgvector extension")
        print("4. (Optional) Set up Neo4j for knowledge graph functionality")
    
    print("\n📚 Documentation:")
    print("- Crawl4AI: https://github.com/unclecode/crawl4ai")
    print("- Crawl4AI RAG MCP: https://github.com/coleam00/mcp-crawl4ai-rag")
    print("- Supabase: https://supabase.com/docs")
    print("- Neo4j: https://neo4j.com/docs")

if __name__ == "__main__":
    main()