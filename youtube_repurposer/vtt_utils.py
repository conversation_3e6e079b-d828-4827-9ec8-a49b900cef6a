import re

def vtt_to_text(vtt_content):
    """
    Convert VTT (Web Video Text Tracks) content to plain text
    
    Args:
        vtt_content (str): The content of a VTT file
        
    Returns:
        str: Plain text with timestamps and formatting removed
    """
    # Remove VTT header and metadata
    text = re.sub(r'^WEBVTT.*\n\n', '', vtt_content)
    
    # Remove timestamps (00:00:00.000 --> 00:00:02.000)
    text = re.sub(r'\d{2}:\d{2}:\d{2}\.\d{3}.*\n', '', text)
    
    # Remove cue identifiers (like "1", "2", etc. on separate lines)
    text = re.sub(r'^\d+$\n', '', text, flags=re.MULTILINE)
    
    # Remove HTML tags and formatting
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove empty lines and extra spaces
    text = re.sub(r'\n+', '\n', text).strip()
    
    return text