# DeepSeek R1 Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-r1
DEEPSEEK_MAX_TOKENS=128000
DEEPSEEK_CHUNK_SIZE=100000
DEEPSEEK_OVERLAP_SIZE=5000
DEEPSEEK_TEMPERATURE=0.7

# Hugging Face Token (for WhisperX speaker diarization)
HF_TOKEN=your_huggingface_token_here

# WhisperX Configuration
WHISPERX_MODEL_SIZE=large-v3
WHISPERX_DEVICE=auto
WHISPERX_COMPUTE_TYPE=float16
WHISPERX_ENABLE_DIARIZATION=true

# Flask Configuration
FLASK_SECRET_KEY=your_secret_key_here
FLASK_DEBUG=false

# File Upload Configuration
MAX_CONTENT_LENGTH=500MB
UPLOAD_FOLDER=uploads
OUTPUT_FOLDER=output

# OpenAI Fallback (optional)
OPENAI_API_KEY=your_openai_api_key_here

# Crawl4AI RAG MCP Configuration
# MCP Server Configuration
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse

# LLM for summaries and contextual embeddings
MODEL_CHOICE=gpt-4o-mini

# RAG Strategies
USE_CONTEXTUAL_EMBEDDINGS=false
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=false
USE_RERANKING=true
USE_KNOWLEDGE_GRAPH=false

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Neo4j Configuration (optional, for knowledge graph functionality)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password