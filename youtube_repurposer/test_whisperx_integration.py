#!/usr/bin/env python3
"""
WhisperX Integration Test

This script tests the WhisperX transcription functionality to ensure
it's properly integrated with the YouTube Repurposer application.
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from whisperx_transcriber import get_transcriber, WhisperXTranscriber, TranscriptionResult
        print("✅ WhisperX transcriber imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_transcriber_creation():
    """Test transcriber creation"""
    print("\nTesting transcriber creation...")
    
    try:
        from whisperx_transcriber import get_transcriber
        
        # Test with different configurations
        transcriber = get_transcriber(
            model_size="tiny",  # Use smallest model for testing
            enable_diarization=False,
            device="cpu"  # Force CPU for compatibility
        )
        
        print("✅ Transcriber created successfully")
        
        # Test model info
        info = transcriber.get_model_info()
        print(f"Model info: {info}")
        
        return True
    except Exception as e:
        print(f"❌ Transcriber creation failed: {e}")
        return False

def create_test_audio():
    """Create a simple test audio file using FFmpeg"""
    print("\nCreating test audio file...")
    
    try:
        # Create a temporary audio file with a simple tone
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            audio_path = temp_file.name
        
        # Generate a 3-second sine wave at 440Hz (A note)
        cmd = [
            'ffmpeg', '-f', 'lavfi',
            '-i', 'sine=frequency=440:duration=3',
            '-ar', '16000',  # 16kHz sample rate
            '-ac', '1',      # Mono
            '-y',            # Overwrite
            audio_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Test audio created: {audio_path}")
            return audio_path
        else:
            print(f"❌ FFmpeg failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Audio creation failed: {e}")
        return None

def test_transcription(audio_path):
    """Test actual transcription"""
    print("\nTesting transcription...")
    
    try:
        from whisperx_transcriber import get_transcriber
        
        transcriber = get_transcriber(
            model_size="tiny",
            enable_diarization=False,
            device="cpu"
        )
        
        # Transcribe the test audio
        result = transcriber.transcribe_file(audio_path)
        
        print(f"✅ Transcription completed")
        print(f"Language: {result.language}")
        print(f"Duration: {result.duration:.2f}s")
        print(f"Word count: {result.word_count}")
        print(f"Confidence: {result.confidence_avg:.2f}")
        print(f"Processing time: {result.processing_time:.2f}s")
        print(f"Text: '{result.full_text}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Transcription failed: {e}")
        return False

def test_app_integration():
    """Test integration with the main app"""
    print("\nTesting app integration...")
    
    try:
        # Test if app.py can import the transcriber
        sys.path.insert(0, '.')
        
        # Check if the import in app.py works
        from whisperx_transcriber import get_transcriber, TranscriptionResult
        print("✅ App integration imports successful")
        
        # Test if we can create a transcriber like the app does
        transcriber = get_transcriber(
            model_size="base",
            enable_diarization=False,
            device="auto"
        )
        print("✅ App-style transcriber creation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration failed: {e}")
        return False

def test_export_formats():
    """Test export functionality"""
    print("\nTesting export formats...")
    
    try:
        from whisperx_transcriber import get_transcriber, TranscriptionSegment, TranscriptionResult
        
        # Create mock transcription result
        segments = [
            TranscriptionSegment(
                start=0.0,
                end=2.0,
                text="Hello, this is a test.",
                confidence=0.95
            ),
            TranscriptionSegment(
                start=2.0,
                end=4.0,
                text="This is another segment.",
                confidence=0.92
            )
        ]
        
        result = TranscriptionResult(
            segments=segments,
            full_text="Hello, this is a test. This is another segment.",
            language="en",
            duration=4.0,
            word_count=9,
            confidence_avg=0.935,
            processing_time=1.0
        )
        
        transcriber = get_transcriber(model_size="tiny", device="cpu")
        
        # Test VTT export
        with tempfile.NamedTemporaryFile(suffix=".vtt", delete=False) as vtt_file:
            transcriber.export_to_vtt(result, vtt_file.name)
            print(f"✅ VTT export successful: {vtt_file.name}")
        
        # Test SRT export
        with tempfile.NamedTemporaryFile(suffix=".srt", delete=False) as srt_file:
            transcriber.export_to_srt(result, srt_file.name)
            print(f"✅ SRT export successful: {srt_file.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Export test failed: {e}")
        return False

def cleanup_test_files():
    """Clean up temporary test files"""
    print("\nCleaning up test files...")
    
    # Remove any temporary files created during testing
    temp_dir = Path(tempfile.gettempdir())
    for file_path in temp_dir.glob("tmp*"):
        if file_path.suffix in ['.wav', '.vtt', '.srt']:
            try:
                file_path.unlink()
                print(f"Removed: {file_path}")
            except:
                pass

def main():
    """Run all tests"""
    print("🎬 WhisperX Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Transcriber Creation", test_transcriber_creation),
        ("App Integration", test_app_integration),
        ("Export Formats", test_export_formats),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    # Optional audio test (requires FFmpeg)
    print(f"\n{'='*20} Audio Test (Optional) {'='*20}")
    audio_path = create_test_audio()
    if audio_path:
        if test_transcription(audio_path):
            print("✅ Audio transcription test passed")
        else:
            print("❌ Audio transcription test failed")
        
        # Clean up
        try:
            os.unlink(audio_path)
        except:
            pass
    else:
        print("⚠️ Audio test skipped (FFmpeg not available)")
    
    # Clean up any remaining test files
    cleanup_test_files()
    
    # Summary
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} core tests passed")
    print("=" * 50)
    
    if passed == total:
        print("🎉 All core tests passed! WhisperX integration is working correctly.")
        print("\nNext steps:")
        print("1. Run 'python app.py' to start the application")
        print("2. Upload a video file to test transcription")
        print("3. Check the transcripts folder for generated files")
        return True
    else:
        print(f"❌ {total - passed} tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure WhisperX is installed: pip install whisperx")
        print("2. Install PyTorch: pip install torch torchaudio")
        print("3. Run the installation script: python install_whisperx.py")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)