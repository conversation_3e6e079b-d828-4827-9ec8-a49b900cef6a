import os
import time
import unittest
from unittest.mock import patch, MagicMock
from app import app
from flask_testing import TestCase
import requests
from unittest.mock import patch

class EndToEndTest(TestCase):
    def create_app(self):
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        return app

    @patch('subprocess.run')
    @patch('app.generate_thumbnail')
    @patch('app.TranscriptProcessor')
    def test_workflow(self, mock_transcript_processor, mock_gen_thumb, mock_subprocess):
        # Mock heavy operations
        mock_subprocess.return_value = None
        mock_gen_thumb.return_value = True
        
        # Mock transcript processor
        mock_processor_instance = mock_transcript_processor.return_value
        mock_processor_instance.process_transcript.return_value = [
            {
                'title': 'Welcome to our tutorial',
                'hook': 'RAG architecture',
                'context': 'This is a test context',
                'conclusion': 'This is a test conclusion',
                'start_ref': '00:01:00',
                'end_ref': '00:02:00'
            }
        ]
        
        # Create in-memory files
        from io import BytesIO
        test_video = BytesIO(b'fake video content')
        test_transcript = BytesIO(b'fake transcript content')
        
        # Upload test files
        response = self.client.post('/upload', data={
            'file': (test_video, 'test_video.mp4'),
            'transcript_file': (test_transcript, 'test_transcript.vtt')
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # Extract job ID from redirect
        job_id = response.request.path.split('/')[-1]
        
        # Monitor progress
        max_checks = 10
        for _ in range(max_checks):
            progress = self.client.get(f'/progress/{job_id}').json
            if progress['status'] == 'completed':
                break
            time.sleep(1)
        else:
            self.fail("Processing didn't complete in time")
        
        # Check results
        results = self.client.get(f'/results/{job_id}').data.decode()
        
        # Verify expected outputs
        self.assertIn('Welcome to our tutorial', results)
        self.assertIn('RAG architecture', results)
        
        # Verify files were created
        self.assertTrue(os.path.exists('static/shorts/test_video_short_0.mp4'))
        self.assertTrue(os.path.exists('static/thumbnails/test_video_thumb_0.jpg'))

if __name__ == '__main__':
    import unittest
    unittest.main()