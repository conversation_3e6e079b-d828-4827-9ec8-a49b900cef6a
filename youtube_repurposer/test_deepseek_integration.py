#!/usr/bin/env python3
"""
Test script for DeepSeek R1 integration with YouTube Repurposer
Tests long transcript analysis and chunking capabilities
"""

import os
import sys
import asyncio
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from deepseek_config import DeepSeekConfig, TokenManager, DeepSeekClient, create_deepseek_client
        print("✅ DeepSeek configuration modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_token_manager():
    """Test token counting and chunking functionality"""
    print("\n🧪 Testing TokenManager...")
    
    try:
        from deepseek_config import TokenManager
        
        # Test with sample long text
        long_text = "This is a test sentence. " * 1000  # ~5000 tokens
        
        manager = TokenManager()
        token_count = manager.count_tokens(long_text)
        print(f"✅ Token counting works: {token_count} tokens")
        
        # Test chunking
        chunks = manager.smart_chunk(long_text, max_tokens=1000)
        print(f"✅ Smart chunking works: {len(chunks)} chunks created")
        
        return True
    except Exception as e:
        print(f"❌ TokenManager test failed: {e}")
        return False

def test_deepseek_client_creation():
    """Test DeepSeek client creation with different configurations"""
    print("\n🧪 Testing DeepSeek client creation...")
    
    try:
        from deepseek_config import create_deepseek_client, DeepSeekClient, DeepSeekConfig
        
        # Test with environment variables
        client = create_deepseek_client()
        if client:
            print("✅ DeepSeek client created from environment variables")
        else:
            print("⚠️ DeepSeek client not created (likely missing API key)")
        
        # Test with manual configuration
        config = DeepSeekConfig(
            api_key="test_key",
            base_url="https://api.deepseek.com",
            model="deepseek-r1",
            max_tokens=128000
        )
        
        manual_client = DeepSeekClient(config)
        print("✅ DeepSeek client created with manual configuration")
        
        return True
    except Exception as e:
        print(f"❌ DeepSeek client creation test failed: {e}")
        return False

async def test_long_transcript_analysis():
    """Test analysis of a long transcript (simulated)"""
    print("\n🧪 Testing long transcript analysis...")
    
    try:
        from deepseek_config import create_deepseek_client
        
        client = create_deepseek_client()
        if not client:
            print("⚠️ Skipping transcript analysis test (no API key)")
            return True
        
        # Create a simulated long transcript (1+ hour content)
        long_transcript = """
        Welcome to this comprehensive tutorial on artificial intelligence and machine learning.
        In this video, we'll cover the fundamentals of neural networks, deep learning architectures,
        and practical applications in real-world scenarios.
        
        First, let's discuss the history of AI and how we got to where we are today.
        The field of artificial intelligence has evolved significantly over the past decades.
        From simple rule-based systems to complex neural networks that can process
        natural language and generate human-like responses.
        
        Now, let's dive into the technical details of how neural networks actually work.
        A neural network consists of layers of interconnected nodes, each performing
        mathematical operations on input data to produce meaningful outputs.
        
        The training process involves feeding the network large amounts of data
        and adjusting the weights and biases to minimize prediction errors.
        This is done through a process called backpropagation.
        
        Moving on to practical applications, we see AI being used in various industries
        including healthcare, finance, transportation, and entertainment.
        Each application requires different approaches and considerations.
        
        In healthcare, AI helps with medical imaging, drug discovery, and patient diagnosis.
        The accuracy and reliability of these systems are crucial for patient safety.
        
        In finance, AI is used for fraud detection, algorithmic trading, and risk assessment.
        The speed and accuracy of these systems can make significant differences in outcomes.
        
        Transportation has been revolutionized by autonomous vehicles and traffic optimization.
        These systems must handle complex real-world scenarios safely and efficiently.
        
        Entertainment applications include recommendation systems, content generation,
        and interactive experiences that adapt to user preferences.
        
        As we conclude this comprehensive overview, it's important to consider
        the ethical implications and future directions of AI development.
        Responsible AI development ensures that these powerful tools benefit humanity
        while minimizing potential risks and biases.
        
        Thank you for watching this detailed exploration of artificial intelligence.
        I hope this content has been informative and helpful for your understanding.
        """ * 20  # Simulate very long content
        
        print(f"📊 Testing with transcript of ~{len(long_transcript.split())} words")
        
        # Test analysis
        result = await client.analyze_long_transcript(long_transcript, "comprehensive")
        
        if 'error' not in result:
            segments = result.get('segments', result.get('top_segments', []))
            print(f"✅ Long transcript analysis successful: {len(segments)} segments identified")
            
            # Print sample segment
            if segments:
                sample = segments[0]
                print(f"📝 Sample segment: {sample.get('title', 'N/A')}")
        else:
            print(f"❌ Analysis failed: {result['error']}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Long transcript analysis test failed: {e}")
        return False

def test_app_integration():
    """Test integration with main app.py"""
    print("\n🧪 Testing app integration...")
    
    try:
        # Test import of updated app.py
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Import key functions
        from app import convert_deepseek_segments
        
        # Test segment conversion
        sample_deepseek_segments = [
            {
                'title': 'Introduction to AI',
                'hook': 'Did you know AI can now...',
                'context': 'This segment covers the basics...',
                'conclusion': 'In summary, AI is transforming...',
                'start_time': '00:00',
                'end_time': '02:30',
                'engagement_score': 0.8,
                'topics': ['AI', 'machine learning'],
                'viral_potential': 'high'
            }
        ]
        
        converted = convert_deepseek_segments(sample_deepseek_segments)
        print(f"✅ Segment conversion works: {len(converted)} segments converted")
        
        return True
    except Exception as e:
        print(f"❌ App integration test failed: {e}")
        return False

def test_rag_integration():
    """Test Enhanced RAG processor integration"""
    print("\n🧪 Testing Enhanced RAG integration...")
    
    try:
        from core.rag.enhanced_rag_processor import EnhancedRAGProcessor
        from deepseek_config import create_deepseek_client
        
        client = create_deepseek_client()
        if not client:
            print("⚠️ Using mock client for RAG test")
            # Create a mock client for testing
            class MockClient:
                async def analyze_content(self, prompt, analysis_type):
                    return {'response': 'Mock response for testing'}
            client = MockClient()
        
        # Initialize RAG processor with DeepSeek client
        rag_processor = EnhancedRAGProcessor(client)
        print("✅ Enhanced RAG processor initialized with DeepSeek client")
        
        return True
    except Exception as e:
        print(f"❌ RAG integration test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 DeepSeek R1 Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Token Manager", test_token_manager),
        ("Client Creation", test_deepseek_client_creation),
        ("Long Transcript Analysis", test_long_transcript_analysis),
        ("App Integration", test_app_integration),
        ("RAG Integration", test_rag_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DeepSeek R1 integration is ready.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        print("\n💡 Troubleshooting tips:")
        print("   1. Ensure DEEPSEEK_API_KEY is set in your environment")
        print("   2. Check internet connection for API calls")
        print("   3. Verify all dependencies are installed")
        print("   4. Run: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)