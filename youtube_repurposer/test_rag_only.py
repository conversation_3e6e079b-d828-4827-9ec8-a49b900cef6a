#!/usr/bin/env python3

import sys
sys.path.append('.')

from rag_processor import TranscriptProcessor

def test_rag_processor():
    # Read the transcript
    with open('uploads/M<PERSON>_Adley_interview.txt', 'r') as f:
        transcript = f.read()
    
    print(f"Transcript length: {len(transcript)} characters")
    print(f"First 200 characters: {transcript[:200]}...")
    print("\n" + "="*50 + "\n")
    
    # Test RAG processor
    print("🔍 Testing RAG Processor:")
    processor = TranscriptProcessor()
    rag_segments = processor.process_transcript(transcript)
    
    print(f"\nRAG segments ({len(rag_segments)} found):")
    for i, segment in enumerate(rag_segments):
        print(f"\nSegment {i+1}:")
        print(f"  title: {segment.get('title', 'N/A')}")
        print(f"  hook: {segment.get('hook', 'N/A')[:100]}...")
        print(f"  context: {segment.get('context', 'N/A')[:100]}...")
        print(f"  conclusion: {segment.get('conclusion', 'N/A')[:100]}...")
        print(f"  start_time: {segment.get('start_time', 'N/A')}")
        print(f"  end_time: {segment.get('end_time', 'N/A')}")
    
    print("\n" + "="*50 + "\n")
    print("✅ RAG test complete!")

if __name__ == "__main__":
    test_rag_processor()