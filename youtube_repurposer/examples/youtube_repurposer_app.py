#!/usr/bin/env python3
"""
YouTube Repurposer Application Example
Demonstrates how to use the ContentApplicationTemplate for YouTube content repurposing
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, Any, List
import json

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

from core.app_template import create_content_app, AppConfig, ProcessingResult
from core.pocketflow import PocketFlow, LLMNode, FunctionNode, ConditionalNode

class YouTubeRepurposerApp:
    """Specialized YouTube content repurposer using the universal template"""
    
    def __init__(self, config_overrides: Dict[str, Any] = None):
        """Initialize the YouTube repurposer application"""
        
        # Default configuration for YouTube repurposing
        default_config = {
            'enable_rag': True,
            'enable_web_crawling': True,
            'enable_context_optimization': True,
            'max_context_tokens': 8000,
            'cache_enabled': True,
            'log_level': 'INFO'
        }
        
        # Merge with any overrides
        if config_overrides:
            default_config.update(config_overrides)
        
        # Create the application using the template
        self.app = create_content_app(
            app_type='youtube_repurposer',
            app_name='youtube_content_repurposer',
            **default_config
        )
        
        # Register YouTube-specific workflows
        self._register_youtube_workflows()
    
    def _register_youtube_workflows(self):
        """Register YouTube-specific workflows"""
        
        # Video to Shorts workflow
        self.app.register_workflow(
            'video_to_shorts',
            self._create_video_to_shorts_workflow(),
            description="Convert long-form video content into engaging short-form clips"
        )
        
        # Thumbnail optimization workflow
        self.app.register_workflow(
            'thumbnail_optimization',
            self._create_thumbnail_workflow(),
            description="Generate optimized thumbnail concepts and metadata"
        )
        
        # Multi-platform distribution workflow
        self.app.register_workflow(
            'multi_platform_distribution',
            self._create_distribution_workflow(),
            description="Adapt content for multiple social media platforms"
        )
        
        # Engagement optimization workflow
        self.app.register_workflow(
            'engagement_optimization',
            self._create_engagement_workflow(),
            description="Optimize content for maximum engagement and virality"
        )
    
    def _create_video_to_shorts_workflow(self) -> PocketFlow:
        """Create workflow for converting videos to shorts"""
        flow = PocketFlow()
        
        # Step 1: Analyze video content
        analysis_node = FunctionNode('analyze_video', self._analyze_video_content)
        
        # Step 2: Identify short-form segments
        segments_node = FunctionNode('identify_segments', self._identify_short_segments)
        
        # Step 3: Generate hooks and CTAs
        hooks_node = FunctionNode('generate_hooks', self._generate_hooks_and_ctas)
        
        # Step 4: Create platform-specific versions
        platform_node = FunctionNode('create_platform_versions', self._create_platform_versions)
        
        # Step 5: Generate metadata
        metadata_node = FunctionNode('generate_metadata', self._generate_shorts_metadata)
        
        # Build workflow
        flow.add_node(analysis_node)
        flow.add_node(segments_node)
        flow.add_node(hooks_node)
        flow.add_node(platform_node)
        flow.add_node(metadata_node)
        
        flow.add_edge('analyze_video', 'identify_segments')
        flow.add_edge('identify_segments', 'generate_hooks')
        flow.add_edge('generate_hooks', 'create_platform_versions')
        flow.add_edge('create_platform_versions', 'generate_metadata')
        
        flow.set_start('analyze_video')
        
        return flow
    
    def _create_thumbnail_workflow(self) -> PocketFlow:
        """Create thumbnail optimization workflow"""
        flow = PocketFlow()
        
        # Step 1: Analyze content themes
        theme_node = FunctionNode('analyze_themes', self._analyze_content_themes)
        
        # Step 2: Generate thumbnail concepts
        concept_node = FunctionNode('generate_concepts', self._generate_thumbnail_concepts)
        
        # Step 3: Optimize for CTR
        ctr_node = FunctionNode('optimize_ctr', self._optimize_thumbnail_ctr)
        
        # Build workflow
        flow.add_node(theme_node)
        flow.add_node(concept_node)
        flow.add_node(ctr_node)
        
        flow.add_edge('analyze_themes', 'generate_concepts')
        flow.add_edge('generate_concepts', 'optimize_ctr')
        
        flow.set_start('analyze_themes')
        
        return flow
    
    def _create_distribution_workflow(self) -> PocketFlow:
        """Create multi-platform distribution workflow"""
        flow = PocketFlow()
        
        # Step 1: Platform analysis
        platform_analysis_node = FunctionNode('analyze_platforms', self._analyze_target_platforms)
        
        # Step 2: Content adaptation
        adaptation_node = FunctionNode('adapt_content', self._adapt_content_for_platforms)
        
        # Step 3: Scheduling optimization
        scheduling_node = FunctionNode('optimize_scheduling', self._optimize_posting_schedule)
        
        # Build workflow
        flow.add_node(platform_analysis_node)
        flow.add_node(adaptation_node)
        flow.add_node(scheduling_node)
        
        flow.add_edge('analyze_platforms', 'adapt_content')
        flow.add_edge('adapt_content', 'optimize_scheduling')
        
        flow.set_start('analyze_platforms')
        
        return flow
    
    def _create_engagement_workflow(self) -> PocketFlow:
        """Create engagement optimization workflow"""
        flow = PocketFlow()
        
        # Step 1: Engagement analysis
        engagement_node = FunctionNode('analyze_engagement', self._analyze_engagement_potential)
        
        # Step 2: Viral elements identification
        viral_node = FunctionNode('identify_viral_elements', self._identify_viral_elements)
        
        # Step 3: Community building
        community_node = FunctionNode('optimize_community', self._optimize_for_community)
        
        # Build workflow
        flow.add_node(engagement_node)
        flow.add_node(viral_node)
        flow.add_node(community_node)
        
        flow.add_edge('analyze_engagement', 'identify_viral_elements')
        flow.add_edge('identify_viral_elements', 'optimize_community')
        
        flow.set_start('analyze_engagement')
        
        return flow
    
    # Workflow step implementations
    async def _analyze_video_content(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze video content for repurposing opportunities"""
        content = state.get('content', '')
        
        # Simulate video content analysis
        analysis = {
            'duration_estimate': '10-15 minutes',
            'main_topics': ['productivity', 'time management', 'work-life balance'],
            'key_moments': [
                {'timestamp': '0:30', 'content': 'Hook - productivity problem introduction'},
                {'timestamp': '2:15', 'content': 'Main technique explanation'},
                {'timestamp': '5:45', 'content': 'Real-world example'},
                {'timestamp': '8:30', 'content': 'Common mistakes to avoid'},
                {'timestamp': '12:00', 'content': 'Call to action'}
            ],
            'engagement_potential': 'high',
            'target_audience': 'professionals, entrepreneurs, students'
        }
        
        state['video_analysis'] = analysis
        return state
    
    async def _identify_short_segments(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Identify segments suitable for short-form content"""
        analysis = state.get('video_analysis', {})
        
        # Generate short-form segments
        segments = [
            {
                'title': 'The #1 Productivity Mistake Everyone Makes',
                'duration': '30-60 seconds',
                'start_time': '0:30',
                'end_time': '1:30',
                'hook': 'You\'re probably making this productivity mistake right now...',
                'content_type': 'problem_revelation',
                'platforms': ['TikTok', 'YouTube Shorts', 'Instagram Reels']
            },
            {
                'title': 'This Simple Technique Changed My Life',
                'duration': '45-90 seconds',
                'start_time': '2:15',
                'end_time': '3:45',
                'hook': 'I discovered this technique by accident, and it changed everything...',
                'content_type': 'solution_reveal',
                'platforms': ['TikTok', 'YouTube Shorts', 'Instagram Reels', 'Twitter']
            },
            {
                'title': 'Why Most People Fail at Time Management',
                'duration': '60 seconds',
                'start_time': '8:30',
                'end_time': '9:30',
                'hook': 'Here\'s why 90% of people fail at time management...',
                'content_type': 'mistake_explanation',
                'platforms': ['LinkedIn', 'YouTube Shorts', 'Instagram Reels']
            }
        ]
        
        state['short_segments'] = segments
        return state
    
    async def _generate_hooks_and_ctas(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate engaging hooks and calls-to-action"""
        segments = state.get('short_segments', [])
        
        for segment in segments:
            # Generate multiple hook variations
            segment['hook_variations'] = [
                segment.get('hook', ''),
                f"POV: {segment.get('title', '').lower()}",
                f"Nobody talks about this: {segment.get('title', '').lower()}",
                f"This will change how you think about {segment.get('content_type', '').replace('_', ' ')}"
            ]
            
            # Generate CTAs
            segment['ctas'] = [
                'Follow for more productivity tips!',
                'Save this for later ↗️',
                'Try this and let me know how it goes!',
                'What\'s your biggest productivity challenge? Comment below!'
            ]
        
        state['enhanced_segments'] = segments
        return state
    
    async def _create_platform_versions(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Create platform-specific versions of content"""
        segments = state.get('enhanced_segments', [])
        
        platform_versions = {}
        
        for segment in segments:
            for platform in segment.get('platforms', []):
                if platform not in platform_versions:
                    platform_versions[platform] = []
                
                # Customize for each platform
                platform_version = {
                    'title': segment['title'],
                    'duration': segment['duration'],
                    'hook': self._customize_hook_for_platform(segment['hook'], platform),
                    'cta': self._customize_cta_for_platform(segment['ctas'][0], platform),
                    'hashtags': self._generate_hashtags_for_platform(segment, platform),
                    'optimal_posting_time': self._get_optimal_posting_time(platform)
                }
                
                platform_versions[platform].append(platform_version)
        
        state['platform_versions'] = platform_versions
        return state
    
    def _customize_hook_for_platform(self, hook: str, platform: str) -> str:
        """Customize hook for specific platform"""
        platform_styles = {
            'TikTok': f"✨ {hook} ✨",
            'Instagram Reels': f"💡 {hook}",
            'YouTube Shorts': hook,
            'LinkedIn': f"Professional insight: {hook}",
            'Twitter': f"🧵 Thread: {hook}"
        }
        return platform_styles.get(platform, hook)
    
    def _customize_cta_for_platform(self, cta: str, platform: str) -> str:
        """Customize CTA for specific platform"""
        platform_ctas = {
            'TikTok': '👆 Follow for daily productivity hacks!',
            'Instagram Reels': '💾 Save this post for later!',
            'YouTube Shorts': '👍 Like if this helped you!',
            'LinkedIn': 'What\'s your experience with this? Share in the comments.',
            'Twitter': 'RT if you found this valuable!'
        }
        return platform_ctas.get(platform, cta)
    
    def _generate_hashtags_for_platform(self, segment: Dict[str, Any], platform: str) -> List[str]:
        """Generate platform-appropriate hashtags"""
        base_tags = ['productivity', 'timemanagement', 'worklifebalance']
        
        platform_tags = {
            'TikTok': base_tags + ['fyp', 'viral', 'lifehacks', 'productivity101'],
            'Instagram Reels': base_tags + ['reels', 'entrepreneur', 'mindset', 'growth'],
            'YouTube Shorts': base_tags + ['shorts', 'tips', 'howto'],
            'LinkedIn': ['productivity', 'professionaldevelopment', 'leadership', 'efficiency'],
            'Twitter': base_tags + ['thread', 'tips', 'career']
        }
        
        return platform_tags.get(platform, base_tags)
    
    def _get_optimal_posting_time(self, platform: str) -> str:
        """Get optimal posting time for platform"""
        optimal_times = {
            'TikTok': '6-10 AM, 7-9 PM EST',
            'Instagram Reels': '11 AM-1 PM, 7-9 PM EST',
            'YouTube Shorts': '2-4 PM, 8-11 PM EST',
            'LinkedIn': '8-10 AM, 12-2 PM EST (weekdays)',
            'Twitter': '9 AM-3 PM EST (weekdays)'
        }
        return optimal_times.get(platform, '9 AM-5 PM EST')
    
    async def _generate_shorts_metadata(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate metadata for short-form content"""
        platform_versions = state.get('platform_versions', {})
        
        metadata = {
            'generation_timestamp': asyncio.get_event_loop().time(),
            'total_platforms': len(platform_versions),
            'content_summary': {
                'total_shorts': sum(len(versions) for versions in platform_versions.values()),
                'estimated_reach': self._calculate_estimated_reach(platform_versions),
                'engagement_prediction': self._predict_engagement(platform_versions)
            },
            'seo_optimization': {
                'primary_keywords': ['productivity', 'time management', 'efficiency'],
                'trending_hashtags': self._get_trending_hashtags(),
                'search_optimization_score': 8.5
            },
            'performance_metrics': {
                'expected_ctr': '3-7%',
                'viral_potential': 'medium-high',
                'audience_retention_prediction': '65-80%'
            }
        }
        
        state['shorts_metadata'] = metadata
        return state
    
    def _calculate_estimated_reach(self, platform_versions: Dict[str, Any]) -> int:
        """Calculate estimated reach across platforms"""
        platform_reach = {
            'TikTok': 50000,
            'Instagram Reels': 30000,
            'YouTube Shorts': 40000,
            'LinkedIn': 15000,
            'Twitter': 20000
        }
        
        total_reach = 0
        for platform in platform_versions.keys():
            total_reach += platform_reach.get(platform, 10000)
        
        return total_reach
    
    def _predict_engagement(self, platform_versions: Dict[str, Any]) -> Dict[str, float]:
        """Predict engagement rates for different platforms"""
        return {
            'TikTok': 0.08,
            'Instagram Reels': 0.06,
            'YouTube Shorts': 0.05,
            'LinkedIn': 0.04,
            'Twitter': 0.03
        }
    
    def _get_trending_hashtags(self) -> List[str]:
        """Get current trending hashtags"""
        return [
            '#productivity', '#timemanagement', '#worklifebalance',
            '#entrepreneur', '#success', '#motivation', '#lifehacks',
            '#efficiency', '#goals', '#mindset'
        ]
    
    # Additional workflow implementations (simplified)
    async def _analyze_content_themes(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content themes for thumbnail creation"""
        state['themes'] = ['productivity', 'success', 'transformation']
        return state
    
    async def _generate_thumbnail_concepts(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate thumbnail concepts"""
        concepts = [
            {
                'concept': 'Before/After Split Screen',
                'description': 'Show transformation or improvement',
                'elements': ['split screen', 'arrows', 'contrasting colors']
            },
            {
                'concept': 'Shocked Face + Text Overlay',
                'description': 'Express surprise or revelation',
                'elements': ['expressive face', 'bold text', 'bright background']
            }
        ]
        state['thumbnail_concepts'] = concepts
        return state
    
    async def _optimize_thumbnail_ctr(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize thumbnails for click-through rate"""
        state['ctr_optimizations'] = {
            'color_scheme': 'high contrast',
            'text_size': 'large and readable',
            'face_expression': 'emotional',
            'composition': 'rule of thirds'
        }
        return state
    
    async def _analyze_target_platforms(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze target platforms for distribution"""
        state['target_platforms'] = {
            'primary': ['YouTube', 'TikTok', 'Instagram'],
            'secondary': ['LinkedIn', 'Twitter', 'Facebook']
        }
        return state
    
    async def _adapt_content_for_platforms(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt content for different platforms"""
        state['platform_adaptations'] = {
            'YouTube': 'Long-form with chapters',
            'TikTok': 'Quick, engaging, trending sounds',
            'Instagram': 'Visual storytelling with carousel posts',
            'LinkedIn': 'Professional insights with industry context'
        }
        return state
    
    async def _optimize_posting_schedule(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize posting schedule across platforms"""
        state['posting_schedule'] = {
            'Monday': 'LinkedIn article',
            'Tuesday': 'YouTube video',
            'Wednesday': 'Instagram carousel',
            'Thursday': 'TikTok short',
            'Friday': 'Twitter thread'
        }
        return state
    
    async def _analyze_engagement_potential(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content for engagement potential"""
        state['engagement_analysis'] = {
            'viral_potential': 'high',
            'shareability_score': 8.5,
            'comment_triggers': ['controversial opinion', 'relatable struggle', 'actionable tip']
        }
        return state
    
    async def _identify_viral_elements(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Identify elements that could make content viral"""
        state['viral_elements'] = {
            'trending_topics': ['productivity hacks', 'work from home'],
            'emotional_triggers': ['frustration', 'hope', 'achievement'],
            'format_suggestions': ['before/after', 'myth busting', 'quick tips']
        }
        return state
    
    async def _optimize_for_community(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize content for community building"""
        state['community_optimization'] = {
            'discussion_starters': [
                'What\'s your biggest productivity challenge?',
                'Share your best time management tip!',
                'How do you stay motivated during busy periods?'
            ],
            'engagement_tactics': [
                'Ask questions in captions',
                'Create polls and quizzes',
                'Respond to every comment',
                'Share user-generated content'
            ]
        }
        return state
    
    # Public API methods
    async def repurpose_video(self, 
                             video_transcript: str, 
                             video_metadata: Dict[str, Any] = None) -> ProcessingResult:
        """Repurpose a video into multiple short-form content pieces"""
        return await self.app.process(
            workflow_name='video_to_shorts',
            content=video_transcript,
            metadata=video_metadata or {},
            content_type='video_transcript'
        )
    
    async def optimize_thumbnails(self, 
                                 video_content: str, 
                                 target_audience: str = None) -> ProcessingResult:
        """Generate optimized thumbnail concepts"""
        return await self.app.process(
            workflow_name='thumbnail_optimization',
            content=video_content,
            target_audience=target_audience
        )
    
    async def create_distribution_plan(self, 
                                      content: str, 
                                      target_platforms: List[str] = None) -> ProcessingResult:
        """Create a multi-platform distribution plan"""
        return await self.app.process(
            workflow_name='multi_platform_distribution',
            content=content,
            target_platforms=target_platforms or ['YouTube', 'TikTok', 'Instagram']
        )
    
    async def optimize_engagement(self, content: str) -> ProcessingResult:
        """Optimize content for maximum engagement"""
        return await self.app.process(
            workflow_name='engagement_optimization',
            content=content
        )
    
    def get_available_workflows(self) -> Dict[str, str]:
        """Get all available workflows"""
        return self.app.get_available_workflows()
    
    async def cleanup(self):
        """Cleanup application resources"""
        await self.app.cleanup()

# Example usage and testing
async def main():
    """Example usage of the YouTube Repurposer App"""
    
    # Sample video transcript
    sample_transcript = """
    Hey everyone, welcome back to my channel! Today I want to share with you 
    the most powerful productivity technique that completely transformed how I work.
    
    It's called the Pomodoro Technique, and I know you've probably heard of it before,
    but I'm going to show you how to actually implement it correctly because most people
    get it wrong.
    
    The basic idea is simple: you work for 25 minutes of focused time, then you take
    a 5-minute break. But here's where most people mess up - they don't actually
    disconnect during the break.
    
    Let me show you the right way to do this...
    
    [Content continues with detailed explanation, examples, and call to action]
    """
    
    # Create the YouTube repurposer app
    repurposer = YouTubeRepurposerApp({
        'log_level': 'DEBUG',
        'cache_enabled': True
    })
    
    try:
        print("🎬 YouTube Repurposer App Demo")
        print("=" * 50)
        
        # Show available workflows
        workflows = repurposer.get_available_workflows()
        print(f"\n📋 Available Workflows ({len(workflows)}):")
        for name, description in workflows.items():
            print(f"  • {name}: {description}")
        
        print("\n🔄 Processing video content...")
        
        # 1. Repurpose video into shorts
        print("\n1️⃣ Creating short-form content...")
        shorts_result = await repurposer.repurpose_video(
            video_transcript=sample_transcript,
            video_metadata={
                'title': 'The Ultimate Productivity Technique',
                'duration': '12:30',
                'views': 15000,
                'engagement_rate': 0.08
            }
        )
        
        if shorts_result.success:
            print(f"   ✅ Generated {len(shorts_result.data.get('platform_versions', {}))} platform versions")
            print(f"   ⏱️  Processing time: {shorts_result.processing_time:.2f}s")
            
            # Show some results
            platform_versions = shorts_result.data.get('platform_versions', {})
            for platform, versions in platform_versions.items():
                print(f"   📱 {platform}: {len(versions)} content pieces")
        else:
            print(f"   ❌ Failed: {shorts_result.errors}")
        
        # 2. Generate thumbnail concepts
        print("\n2️⃣ Generating thumbnail concepts...")
        thumbnail_result = await repurposer.optimize_thumbnails(
            video_content=sample_transcript,
            target_audience="professionals and entrepreneurs"
        )
        
        if thumbnail_result.success:
            concepts = thumbnail_result.data.get('thumbnail_concepts', [])
            print(f"   ✅ Generated {len(concepts)} thumbnail concepts")
            for i, concept in enumerate(concepts, 1):
                print(f"   🎨 Concept {i}: {concept.get('concept', 'N/A')}")
        
        # 3. Create distribution plan
        print("\n3️⃣ Creating distribution plan...")
        distribution_result = await repurposer.create_distribution_plan(
            content=sample_transcript,
            target_platforms=['YouTube', 'TikTok', 'Instagram', 'LinkedIn']
        )
        
        if distribution_result.success:
            schedule = distribution_result.data.get('posting_schedule', {})
            print(f"   ✅ Created posting schedule for {len(schedule)} days")
            for day, content_type in schedule.items():
                print(f"   📅 {day}: {content_type}")
        
        # 4. Optimize for engagement
        print("\n4️⃣ Optimizing for engagement...")
        engagement_result = await repurposer.optimize_engagement(sample_transcript)
        
        if engagement_result.success:
            analysis = engagement_result.data.get('engagement_analysis', {})
            viral_potential = analysis.get('viral_potential', 'unknown')
            shareability = analysis.get('shareability_score', 0)
            print(f"   ✅ Viral potential: {viral_potential}")
            print(f"   📊 Shareability score: {shareability}/10")
        
        print("\n🎉 Demo completed successfully!")
        print("\n💡 Next steps:")
        print("   • Integrate with video editing tools")
        print("   • Add automated posting capabilities")
        print("   • Implement A/B testing for thumbnails")
        print("   • Connect to analytics APIs for performance tracking")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
    
    finally:
        # Cleanup
        await repurposer.cleanup()
        print("\n🧹 Cleanup completed")

if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())