#!/usr/bin/env python3
"""
Custom Application Examples

This file demonstrates how to create various content processing applications
using the Enhanced YouTube Repurposer template system.
"""

import asyncio
from typing import Dict, Any, List
from core.app_template import ContentApplicationTemplate, AppConfig, ProcessingResult
from core.pocketflow import <PERSON><PERSON>low, LLMNode, FunctionNode


class SocialMediaOptimizerApp(ContentApplicationTemplate):
    """Specialized app for optimizing content across social media platforms."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platforms = config.get('platforms', ['instagram', 'tiktok', 'twitter'])
        self._register_social_workflows()
    
    def _register_social_workflows(self):
        """Register social media specific workflows."""
        
        # Platform-specific optimization workflow
        platform_workflow = PocketFlow("platform_optimization")
        
        # Analyze content for platform fit
        platform_workflow.add_node(LLMNode(
            "platform_analyzer",
            system_prompt="Analyze content for social media platform optimization.",
            user_prompt="Content: {content}\n\nAnalyze this content for optimization across these platforms: {platforms}. Provide specific recommendations for each platform including optimal length, hashtags, posting times, and engagement strategies."
        ))
        
        # Generate platform-specific versions
        platform_workflow.add_node(FunctionNode(
            "platform_generator",
            self._generate_platform_versions
        ))
        
        self.app.register_workflow("platform_optimization", platform_workflow)
        
        # Hashtag optimization workflow
        hashtag_workflow = PocketFlow("hashtag_optimization")
        
        hashtag_workflow.add_node(LLMNode(
            "hashtag_generator",
            system_prompt="You are a social media hashtag expert.",
            user_prompt="Content: {content}\nPlatform: {platform}\n\nGenerate 20 relevant hashtags for this content on {platform}. Include a mix of popular, niche, and trending hashtags. Format as a JSON list."
        ))
        
        self.app.register_workflow("hashtag_optimization", hashtag_workflow)
    
    def _generate_platform_versions(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate platform-specific content versions."""
        content = context.get('content', '')
        analysis = context.get('platform_analyzer', '')
        
        # This would typically use the LLM to generate different versions
        # For now, return a structured response
        return {
            'platform_versions': {
                'instagram': f"📸 {content[:100]}... #content #instagram",
                'tiktok': f"🎵 {content[:80]}... #fyp #viral",
                'twitter': f"🧵 {content[:200]}... #thread"
            },
            'analysis': analysis
        }
    
    async def optimize_for_platforms(self, content: str, target_platforms: List[str] = None) -> ProcessingResult:
        """Optimize content for specific social media platforms."""
        platforms = target_platforms or self.platforms
        
        result = await self.app.process(
            workflow_name='platform_optimization',
            content=content,
            platforms=platforms
        )
        
        return result
    
    async def generate_hashtags(self, content: str, platform: str) -> ProcessingResult:
        """Generate optimized hashtags for a specific platform."""
        result = await self.app.process(
            workflow_name='hashtag_optimization',
            content=content,
            platform=platform
        )
        
        return result


class ScriptGeneratorApp(ContentApplicationTemplate):
    """Specialized app for generating various types of scripts."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.script_types = config.get('script_types', ['educational', 'entertainment', 'commercial'])
        self._register_script_workflows()
    
    def _register_script_workflows(self):
        """Register script generation workflows."""
        
        # Educational script workflow
        edu_workflow = PocketFlow("educational_script")
        
        edu_workflow.add_node(LLMNode(
            "topic_analyzer",
            system_prompt="You are an educational content expert.",
            user_prompt="Topic: {topic}\nTarget Audience: {audience}\nDuration: {duration} minutes\n\nAnalyze this topic and create a detailed outline for an educational script. Include learning objectives, key points, examples, and engagement strategies."
        ))
        
        edu_workflow.add_node(LLMNode(
            "script_writer",
            system_prompt="You are a professional script writer specializing in educational content.",
            user_prompt="Based on this outline: {topic_analyzer}\n\nWrite a complete educational script that is engaging, informative, and appropriate for the target audience. Include stage directions, timing cues, and interactive elements."
        ))
        
        self.app.register_workflow("educational_script", edu_workflow)
        
        # Commercial script workflow
        commercial_workflow = PocketFlow("commercial_script")
        
        commercial_workflow.add_node(LLMNode(
            "product_analyzer",
            system_prompt="You are a marketing and advertising expert.",
            user_prompt="Product/Service: {product}\nTarget Audience: {audience}\nKey Benefits: {benefits}\nCall to Action: {cta}\n\nAnalyze this product and create a compelling commercial script structure focusing on emotional connection and clear value proposition."
        ))
        
        commercial_workflow.add_node(LLMNode(
            "commercial_writer",
            system_prompt="You are a professional commercial script writer.",
            user_prompt="Based on this analysis: {product_analyzer}\n\nWrite a persuasive commercial script that captures attention, builds desire, and drives action. Include timing, visual cues, and multiple CTA options."
        ))
        
        self.app.register_workflow("commercial_script", commercial_workflow)
    
    async def generate_educational_script(self, topic: str, audience: str, duration: int) -> ProcessingResult:
        """Generate an educational script."""
        result = await self.app.process(
            workflow_name='educational_script',
            topic=topic,
            audience=audience,
            duration=duration
        )
        
        return result
    
    async def generate_commercial_script(self, product: str, audience: str, benefits: str, cta: str) -> ProcessingResult:
        """Generate a commercial script."""
        result = await self.app.process(
            workflow_name='commercial_script',
            product=product,
            audience=audience,
            benefits=benefits,
            cta=cta
        )
        
        return result


class SEOContentGeneratorApp(ContentApplicationTemplate):
    """Specialized app for generating SEO-optimized content."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self._register_seo_workflows()
    
    def _register_seo_workflows(self):
        """Register SEO content workflows."""
        
        # Keyword research workflow
        keyword_workflow = PocketFlow("keyword_research")
        
        keyword_workflow.add_node(LLMNode(
            "keyword_analyzer",
            system_prompt="You are an SEO expert specializing in keyword research.",
            user_prompt="Topic: {topic}\nTarget Audience: {audience}\nContent Type: {content_type}\n\nGenerate a comprehensive keyword strategy including primary keywords, long-tail keywords, semantic keywords, and related terms. Consider search intent and competition level."
        ))
        
        self.app.register_workflow("keyword_research", keyword_workflow)
        
        # SEO content creation workflow
        seo_content_workflow = PocketFlow("seo_content_creation")
        
        seo_content_workflow.add_node(LLMNode(
            "content_planner",
            system_prompt="You are an SEO content strategist.",
            user_prompt="Keywords: {keywords}\nTopic: {topic}\nTarget Length: {target_length} words\n\nCreate a detailed content outline that naturally incorporates these keywords while providing valuable information to readers. Include H1, H2, H3 structure and meta descriptions."
        ))
        
        seo_content_workflow.add_node(LLMNode(
            "content_writer",
            system_prompt="You are a professional SEO content writer.",
            user_prompt="Based on this outline: {content_planner}\n\nWrite high-quality, SEO-optimized content that reads naturally while incorporating target keywords. Include proper heading structure, internal linking opportunities, and engaging meta descriptions."
        ))
        
        self.app.register_workflow("seo_content_creation", seo_content_workflow)
    
    async def research_keywords(self, topic: str, audience: str, content_type: str) -> ProcessingResult:
        """Research keywords for a topic."""
        result = await self.app.process(
            workflow_name='keyword_research',
            topic=topic,
            audience=audience,
            content_type=content_type
        )
        
        return result
    
    async def create_seo_content(self, keywords: str, topic: str, target_length: int) -> ProcessingResult:
        """Create SEO-optimized content."""
        result = await self.app.process(
            workflow_name='seo_content_creation',
            keywords=keywords,
            topic=topic,
            target_length=target_length
        )
        
        return result


# Factory functions for easy app creation
def create_social_media_optimizer(platforms: List[str] = None, **kwargs) -> SocialMediaOptimizerApp:
    """Create a social media optimizer app."""
    config = {
        'platforms': platforms or ['instagram', 'tiktok', 'twitter'],
        'enable_rag': kwargs.get('enable_rag', True),
        'enable_context_optimization': kwargs.get('enable_context_optimization', True),
        **kwargs
    }
    return SocialMediaOptimizerApp(config)


def create_script_generator(script_types: List[str] = None, **kwargs) -> ScriptGeneratorApp:
    """Create a script generator app."""
    config = {
        'script_types': script_types or ['educational', 'entertainment', 'commercial'],
        'enable_rag': kwargs.get('enable_rag', True),
        'enable_context_optimization': kwargs.get('enable_context_optimization', True),
        **kwargs
    }
    return ScriptGeneratorApp(config)


def create_seo_content_generator(**kwargs) -> SEOContentGeneratorApp:
    """Create an SEO content generator app."""
    config = {
        'enable_rag': kwargs.get('enable_rag', True),
        'enable_context_optimization': kwargs.get('enable_context_optimization', True),
        'enable_web_crawling': kwargs.get('enable_web_crawling', True),  # Useful for SEO research
        **kwargs
    }
    return SEOContentGeneratorApp(config)


# Example usage
async def main():
    """Demonstrate custom app creation and usage."""
    print("🚀 Creating Custom Content Applications")
    print("=" * 50)
    
    # Create social media optimizer
    print("\n📱 Creating Social Media Optimizer...")
    social_app = create_social_media_optimizer(
        platforms=['instagram', 'tiktok'],
        enable_rag=False  # Disable for demo
    )
    
    # Test social media optimization
    sample_content = "Just discovered an amazing productivity hack that saves me 2 hours every day!"
    
    try:
        result = await social_app.optimize_for_platforms(sample_content)
        if result.success:
            print(f"✅ Social optimization completed in {result.processing_time:.2f}s")
            print(f"📊 Generated versions: {list(result.data.get('platform_versions', {}).keys())}")
        else:
            print(f"❌ Social optimization failed: {result.error}")
    except Exception as e:
        print(f"⚠️ Social app demo skipped: {e}")
    
    # Create script generator
    print("\n🎬 Creating Script Generator...")
    script_app = create_script_generator(
        script_types=['educational'],
        enable_rag=False  # Disable for demo
    )
    
    # Test script generation
    try:
        result = await script_app.generate_educational_script(
            topic="Introduction to Machine Learning",
            audience="Beginners",
            duration=10
        )
        if result.success:
            print(f"✅ Script generation completed in {result.processing_time:.2f}s")
            print(f"📝 Script length: {len(result.data.get('script_writer', ''))} characters")
        else:
            print(f"❌ Script generation failed: {result.error}")
    except Exception as e:
        print(f"⚠️ Script app demo skipped: {e}")
    
    # Create SEO content generator
    print("\n🔍 Creating SEO Content Generator...")
    seo_app = create_seo_content_generator(
        enable_rag=False,  # Disable for demo
        enable_web_crawling=False
    )
    
    # Test SEO content creation
    try:
        result = await seo_app.research_keywords(
            topic="Sustainable Living Tips",
            audience="Environmentally conscious consumers",
            content_type="blog_post"
        )
        if result.success:
            print(f"✅ Keyword research completed in {result.processing_time:.2f}s")
            print(f"🎯 Keywords generated: {len(result.data.get('keyword_analyzer', '').split())} words")
        else:
            print(f"❌ Keyword research failed: {result.error}")
    except Exception as e:
        print(f"⚠️ SEO app demo skipped: {e}")
    
    print("\n🎉 Custom app demonstration completed!")
    print("\n💡 Tips for creating your own apps:")
    print("   1. Inherit from ContentApplicationTemplate")
    print("   2. Register custom workflows in __init__")
    print("   3. Create specialized methods for your use case")
    print("   4. Use factory functions for easy instantiation")
    print("   5. Test with simple examples before complex workflows")


if __name__ == "__main__":
    asyncio.run(main())