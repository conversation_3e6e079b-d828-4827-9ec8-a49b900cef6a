
import pytest
import asyncio
from core.app_template import create_content_app
from examples.youtube_repurposer_app import YouTubeR<PERSON>urposerApp

@pytest.mark.asyncio
async def test_enhanced_app_initialization():
    """Test that the enhanced app initializes correctly."""
    config = {
        'log_level': 'INFO',
        'cache_enabled': False,
        'enable_rag': False,
        'enable_context_optimization': False,
        'enable_web_crawling': False
    }
    
    app = YouTubeRepurposerApp(config)
    assert app is not None
    assert app.app is not None

@pytest.mark.asyncio
async def test_content_analysis():
    """Test basic content analysis workflow."""
    config = {
        'log_level': 'INFO',
        'cache_enabled': False,
        'enable_rag': False,
        'enable_context_optimization': False,
        'enable_web_crawling': False
    }
    
    app = YouTubeRepurposerApp(config)
    
    # Test with sample content
    sample_transcript = "This is a test video about AI and machine learning."
    
    try:
        result = await app.app.process(
            workflow_name='content_analysis',
            content=sample_transcript,
            content_type='video_transcript'
        )
        
        assert result is not None
        # Note: This might fail without proper API keys
        
    except Exception as e:
        # Expected to fail without API keys
        assert "API key" in str(e) or "authentication" in str(e).lower()
