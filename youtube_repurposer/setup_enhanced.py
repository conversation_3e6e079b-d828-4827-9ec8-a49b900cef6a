#!/usr/bin/env python3
"""
Enhanced YouTube Repurposer Setup Script

This script sets up the enhanced template system with all required dependencies
and initializes the necessary components for the content application template.
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def run_command(command, description):
    """Run a shell command with error handling."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def create_directories():
    """Create necessary directories for the enhanced system."""
    directories = [
        'uploads',
        'data',
        'data/vector_store',
        'data/cache',
        'logs',
        'core',
        'core/agents',
        'core/rag',
        'core/context_engineering',
        'examples',
        'tests'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    return True

def setup_vector_database():
    """Initialize the SQLite vector database."""
    db_path = 'data/vector_store/embeddings.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create embeddings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS embeddings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content_hash TEXT UNIQUE,
                content TEXT,
                embedding BLOB,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create index for faster searches
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_content_hash ON embeddings(content_hash)
        ''')
        
        conn.commit()
        conn.close()
        
        print(f"✅ Vector database initialized at {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize vector database: {e}")
        return False

def create_env_template():
    """Create a template .env file if it doesn't exist."""
    env_path = '.env'
    
    if os.path.exists(env_path):
        print("✅ .env file already exists")
        return True
    
    env_template = '''
# API Keys
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Application Settings
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# Enhanced Features
ENABLE_RAG=true
ENABLE_CONTEXT_OPTIMIZATION=true
ENABLE_WEB_CRAWLING=false
CACHE_ENABLED=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Vector Store Settings
VECTOR_STORE_PATH=data/vector_store/embeddings.db
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Content Processing
MAX_CONTENT_LENGTH=10000
CONTEXT_WINDOW_SIZE=4000
MAX_TOKENS=2000
'''
    
    try:
        with open(env_path, 'w') as f:
            f.write(env_template)
        print(f"✅ Created .env template at {env_path}")
        print("⚠️  Please update the API keys in .env file")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env template: {e}")
        return False

def install_dependencies():
    """Install Python dependencies."""
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install -r requirements.txt", "Installing Python dependencies"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True

def download_nlp_models():
    """Download required NLP models."""
    print("\n🔄 Downloading NLP models...")
    
    try:
        # Download spaCy model
        subprocess.run(["python", "-m", "spacy", "download", "en_core_web_sm"], check=True)
        print("✅ spaCy English model downloaded")
        
        # Download NLTK data
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        nltk.download('vader_lexicon', quiet=True)
        print("✅ NLTK data downloaded")
        
        return True
        
    except Exception as e:
        print(f"⚠️  NLP model download failed: {e}")
        print("   Models will be downloaded on first use")
        return True  # Non-critical failure

def create_test_files():
    """Create basic test files."""
    test_content = '''
import pytest
import asyncio
from core.app_template import create_content_app
from examples.youtube_repurposer_app import YouTubeRepurposerApp

@pytest.mark.asyncio
async def test_enhanced_app_initialization():
    """Test that the enhanced app initializes correctly."""
    config = {
        'log_level': 'INFO',
        'cache_enabled': False,
        'enable_rag': False,
        'enable_context_optimization': False,
        'enable_web_crawling': False
    }
    
    app = YouTubeRepurposerApp(config)
    assert app is not None
    assert app.app is not None

@pytest.mark.asyncio
async def test_content_analysis():
    """Test basic content analysis workflow."""
    config = {
        'log_level': 'INFO',
        'cache_enabled': False,
        'enable_rag': False,
        'enable_context_optimization': False,
        'enable_web_crawling': False
    }
    
    app = YouTubeRepurposerApp(config)
    
    # Test with sample content
    sample_transcript = "This is a test video about AI and machine learning."
    
    try:
        result = await app.app.process(
            workflow_name='content_analysis',
            content=sample_transcript,
            content_type='video_transcript'
        )
        
        assert result is not None
        # Note: This might fail without proper API keys
        
    except Exception as e:
        # Expected to fail without API keys
        assert "API key" in str(e) or "authentication" in str(e).lower()
'''
    
    try:
        with open('tests/test_enhanced_app.py', 'w') as f:
            f.write(test_content)
        print("✅ Created test files")
        return True
    except Exception as e:
        print(f"❌ Failed to create test files: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Enhanced YouTube Repurposer Template System")
    print("=" * 60)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    # Setup steps
    steps = [
        (create_directories, "Creating directories"),
        (install_dependencies, "Installing dependencies"),
        (setup_vector_database, "Setting up vector database"),
        (create_env_template, "Creating environment template"),
        (download_nlp_models, "Downloading NLP models"),
        (create_test_files, "Creating test files")
    ]
    
    failed_steps = []
    
    for step_func, step_name in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            failed_steps.append(step_name)
    
    # Summary
    print("\n" + "=" * 60)
    if not failed_steps:
        print("🎉 Setup completed successfully!")
        print("\n📝 Next steps:")
        print("   1. Update API keys in .env file")
        print("   2. Run: python app.py")
        print("   3. Visit: http://localhost:5000")
        print("   4. Run tests: pytest tests/")
    else:
        print(f"⚠️  Setup completed with {len(failed_steps)} warnings:")
        for step in failed_steps:
            print(f"   - {step}")
        print("\n📝 You may need to manually complete the failed steps.")
    
    print("\n🔗 Enhanced features available:")
    print("   - PocketFlow workflow system")
    print("   - Enhanced RAG with vector embeddings")
    print("   - Context engineering and optimization")
    print("   - Content analysis agents")
    print("   - Template system for creating new apps")

if __name__ == "__main__":
    main()