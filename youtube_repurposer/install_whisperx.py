#!/usr/bin/env python3
"""
WhisperX Installation Script

This script helps install WhisperX and its dependencies for the YouTube Repurposer.
It handles different platforms and provides fallback options.
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def run_command(cmd, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"Running: {description or ' '.join(cmd)}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Success!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0]}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported. Please use Python 3.8 or higher.")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_ffmpeg():
    """Check if FFmpeg is installed"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg is installed")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ FFmpeg not found")
    return False

def install_ffmpeg():
    """Install FFmpeg based on platform"""
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        print("Installing FFmpeg on macOS using Homebrew...")
        if run_command(['brew', '--version'], "Checking Homebrew"):
            return run_command(['brew', 'install', 'ffmpeg'], "Installing FFmpeg with Homebrew")
        else:
            print("❌ Homebrew not found. Please install Homebrew first: https://brew.sh/")
            return False
    
    elif system == "linux":
        print("Installing FFmpeg on Linux...")
        # Try different package managers
        if run_command(['which', 'apt'], "Checking apt"):
            return run_command(['sudo', 'apt', 'update'], "Updating apt") and \
                   run_command(['sudo', 'apt', 'install', '-y', 'ffmpeg'], "Installing FFmpeg with apt")
        elif run_command(['which', 'yum'], "Checking yum"):
            return run_command(['sudo', 'yum', 'install', '-y', 'ffmpeg'], "Installing FFmpeg with yum")
        elif run_command(['which', 'dnf'], "Checking dnf"):
            return run_command(['sudo', 'dnf', 'install', '-y', 'ffmpeg'], "Installing FFmpeg with dnf")
        else:
            print("❌ No supported package manager found. Please install FFmpeg manually.")
            return False
    
    elif system == "windows":
        print("❌ Automatic FFmpeg installation on Windows is not supported.")
        print("Please download FFmpeg from: https://ffmpeg.org/download.html")
        print("And add it to your PATH.")
        return False
    
    else:
        print(f"❌ Unsupported platform: {system}")
        return False

def install_pytorch():
    """Install PyTorch with appropriate backend"""
    system = platform.system().lower()
    
    # Check if CUDA is available
    cuda_available = False
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True)
        cuda_available = result.returncode == 0
    except FileNotFoundError:
        pass
    
    if cuda_available:
        print("🔥 CUDA detected, installing PyTorch with CUDA support...")
        cmd = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchaudio', '--index-url', 'https://download.pytorch.org/whl/cu118']
    elif system == "darwin" and platform.machine() == "arm64":  # Apple Silicon
        print("🍎 Apple Silicon detected, installing PyTorch with MPS support...")
        cmd = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchaudio']
    else:
        print("💻 Installing PyTorch with CPU support...")
        cmd = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchaudio', '--index-url', 'https://download.pytorch.org/whl/cpu']
    
    return run_command(cmd, "Installing PyTorch")

def install_whisperx():
    """Install WhisperX and dependencies"""
    print("Installing WhisperX...")
    
    # Install WhisperX
    if not run_command([sys.executable, '-m', 'pip', 'install', 'whisperx'], "Installing WhisperX"):
        return False
    
    # Install additional dependencies
    additional_deps = [
        'faster-whisper>=0.9.0',
        'pyannote.audio>=3.1.0',
        'huggingface-hub>=0.16.0',
        'transformers>=4.21.0'
    ]
    
    for dep in additional_deps:
        if not run_command([sys.executable, '-m', 'pip', 'install', dep], f"Installing {dep}"):
            print(f"⚠️ Warning: Failed to install {dep}")
    
    return True

def test_whisperx():
    """Test WhisperX installation"""
    print("\nTesting WhisperX installation...")
    
    try:
        import whisperx
        import torch
        
        print("✅ WhisperX imported successfully")
        print(f"✅ PyTorch version: {torch.__version__}")
        
        # Test device detection
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print("✅ Apple MPS available")
        else:
            print("✅ CPU backend available")
        
        # Test model loading (small test)
        try:
            print("Testing model loading...")
            device = "cuda" if torch.cuda.is_available() else "cpu"
            model = whisperx.load_model("tiny", device)
            print("✅ Model loading successful")
            return True
        except Exception as e:
            print(f"⚠️ Model loading failed: {e}")
            print("This might be due to missing models - they will download on first use.")
            return True
            
    except ImportError as e:
        print(f"❌ WhisperX import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def create_env_example():
    """Create .env.example with WhisperX settings"""
    env_content = """
# WhisperX Configuration
WHISPERX_MODEL_SIZE=base
WHISPERX_DEVICE=auto
WHISPERX_COMPUTE_TYPE=float16
WHISPERX_ENABLE_DIARIZATION=false

# HuggingFace Token (required for speaker diarization)
# Get from: https://huggingface.co/settings/tokens
HF_TOKEN=your_huggingface_token_here

# Existing configuration...
OPENAI_API_KEY=your_openai_api_key_here
"""
    
    env_path = Path(".env.example")
    if not env_path.exists():
        with open(env_path, 'w') as f:
            f.write(env_content)
        print(f"✅ Created {env_path}")
    else:
        print(f"ℹ️ {env_path} already exists")

def main():
    """Main installation process"""
    print("🎬 YouTube Repurposer - WhisperX Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check and install FFmpeg
    if not check_ffmpeg():
        print("\nFFmpeg is required for audio processing.")
        install_choice = input("Would you like to try installing FFmpeg automatically? (y/n): ")
        if install_choice.lower() == 'y':
            if not install_ffmpeg():
                print("❌ FFmpeg installation failed. Please install manually.")
                sys.exit(1)
        else:
            print("Please install FFmpeg manually and run this script again.")
            sys.exit(1)
    
    # Install PyTorch
    print("\nInstalling PyTorch...")
    if not install_pytorch():
        print("❌ PyTorch installation failed")
        sys.exit(1)
    
    # Install WhisperX
    print("\nInstalling WhisperX...")
    if not install_whisperx():
        print("❌ WhisperX installation failed")
        sys.exit(1)
    
    # Test installation
    if not test_whisperx():
        print("❌ WhisperX test failed")
        sys.exit(1)
    
    # Create environment example
    create_env_example()
    
    print("\n" + "=" * 50)
    print("🎉 WhisperX installation completed successfully!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Copy .env.example to .env and configure your settings")
    print("2. If you want speaker diarization, get a HuggingFace token from:")
    print("   https://huggingface.co/settings/tokens")
    print("3. Run the application: python app.py")
    print("\nThe first time you use WhisperX, it will download the required models.")
    print("This may take a few minutes depending on your internet connection.")

if __name__ == "__main__":
    main()